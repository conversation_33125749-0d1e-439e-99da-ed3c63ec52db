-- Complete Database Dump (Schema + Data)
-- Generated on: 2025-07-18T02:00:45.912Z
-- Source: https://hhdxsyocvrmyxcuiqtzn.supabase.co

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create tables (schema)

-- Schema for team_members

CREATE TABLE IF NOT EXISTS public.team_members (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  role TEXT NOT NULL,
  department TEXT,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Schema for impact_types

CREATE TABLE IF NOT EXISTS public.impact_types (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  label TEXT NOT NULL,
  description TEXT,
  color TEXT,
  bg_color TEXT,
  border_color TEXT,
  is_active BOOLEAN NOT NULL DEFAULT true,
  sort_order INTEGER,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Schema for projects

CREATE TABLE IF NOT EXISTS public.projects (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('internal', 'external')),
  customer_name TEXT,
  project_lead TEXT NOT NULL,
  customer_lead TEXT,
  customer_contact TEXT,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'on_hold', 'archived')),
  priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  start_date DATE,
  end_date DATE,
  budget DECIMAL,
  progress INTEGER DEFAULT 0,
  requirements_status TEXT,
  prd_document_link TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Schema for tasks

CREATE TABLE IF NOT EXISTS public.tasks (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  assignee TEXT NOT NULL,
  due_date DATE,
  status TEXT NOT NULL DEFAULT 'to-do' CHECK (status IN ('to-do', 'in-progress', 'done')),
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  estimated_hours INTEGER,
  actual_hours INTEGER,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Schema for sub_tasks

CREATE TABLE IF NOT EXISTS public.sub_tasks (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  task_id UUID NOT NULL REFERENCES public.tasks(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  assignee TEXT NOT NULL,
  due_date DATE,
  status TEXT NOT NULL DEFAULT 'to-do' CHECK (status IN ('to-do', 'in-progress', 'done')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Schema for priority_history

CREATE TABLE IF NOT EXISTS public.priority_history (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
  old_priority TEXT,
  new_priority TEXT NOT NULL,
  reason TEXT,
  changed_by TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Schema for priority_rules

CREATE TABLE IF NOT EXISTS public.priority_rules (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  from_priority TEXT NOT NULL,
  to_priority TEXT NOT NULL,
  max_days INTEGER NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Schema for project_integrations

CREATE TABLE IF NOT EXISTS public.project_integrations (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
  integration_type TEXT NOT NULL CHECK (integration_type IN ('google_drive', 'slack_webhook')),
  integration_url TEXT NOT NULL,
  integration_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Insert data
SET session_replication_role = replica;

-- Insert data for team_members
TRUNCATE TABLE team_members RESTART IDENTITY CASCADE;
INSERT INTO team_members (id, name, email, role, department, is_active, created_at, updated_at) VALUES ('38e0aa56-df19-40fe-b02e-0aa7d07e76cf', 'Jackson Tumbridge ', '<EMAIL>', 'Opperations Associate ', 'Australia', TRUE, '2025-07-14T03:10:02.597922+00:00', '2025-07-14T04:41:20.975661+00:00');
INSERT INTO team_members (id, name, email, role, department, is_active, created_at, updated_at) VALUES ('4718b85c-5ca1-4d4a-8b42-c221466e2bd4', 'Grant Moyle', '<EMAIL>', 'CEO', 'Australia', TRUE, '2025-07-12T23:02:44.807323+00:00', '2025-07-14T04:41:26.559479+00:00');
INSERT INTO team_members (id, name, email, role, department, is_active, created_at, updated_at) VALUES ('a9c89b12-c9cb-4727-baec-41493ccb80c7', 'Kavi Koneti ', '<EMAIL>', 'Vice President of AI', 'India', TRUE, '2025-07-14T03:07:33.752892+00:00', '2025-07-14T04:41:32.107322+00:00');
INSERT INTO team_members (id, name, email, role, department, is_active, created_at, updated_at) VALUES ('94bcd00f-964e-4a50-adb6-e855be9ad3c0', 'Will White ', '<EMAIL>', 'Senior AI Engineer', 'Australia', TRUE, '2025-07-14T03:04:39.06417+00:00', '2025-07-14T04:41:38.135416+00:00');
INSERT INTO team_members (id, name, email, role, department, is_active, created_at, updated_at) VALUES ('ee2f08a6-a525-4937-9795-658cebccfed0', 'Arjuna Kuraganti', '<EMAIL>', 'Developer', 'India', TRUE, '2025-07-16T21:58:46.011413+00:00', '2025-07-16T21:58:46.011413+00:00');

-- Insert data for impact_types
TRUNCATE TABLE impact_types RESTART IDENTITY CASCADE;
INSERT INTO impact_types (id, name, label, description, color, bg_color, border_color, is_active, sort_order, created_at, updated_at) VALUES ('a4a2a411-867f-4aa6-a667-e4f28d8f8804', 'Platform', 'Platform', 'Core infrastructure, agent framework, future mission', 'hsl(var(--blue))', 'hsl(var(--blue) / 0.1)', 'hsl(var(--blue) / 0.3)', TRUE, 2, '2025-07-17T07:46:04.382726+00:00', '2025-07-17T07:46:04.382726+00:00');
INSERT INTO impact_types (id, name, label, description, color, bg_color, border_color, is_active, sort_order, created_at, updated_at) VALUES ('d7b98af1-031b-4aff-8c03-dd09d7b871f4', 'Bug Fix', 'Bug Fix', 'Resolving existing issues/technical problems', 'hsl(var(--purple))', 'hsl(var(--purple) / 0.1)', 'hsl(var(--purple) / 0.3)', TRUE, 3, '2025-07-17T07:46:04.382726+00:00', '2025-07-17T07:46:04.382726+00:00');
INSERT INTO impact_types (id, name, label, description, color, bg_color, border_color, is_active, sort_order, created_at, updated_at) VALUES ('516d941e-fc49-483f-ac76-79b8f944812f', 'R&D', 'R&D', 'Research, experimentation, proof of concepts', 'hsl(var(--orange))', 'hsl(var(--orange) / 0.1)', 'hsl(var(--orange) / 0.3)', TRUE, 4, '2025-07-17T07:46:04.382726+00:00', '2025-07-17T07:46:04.382726+00:00');
INSERT INTO impact_types (id, name, label, description, color, bg_color, border_color, is_active, sort_order, created_at, updated_at) VALUES ('efd7ddfd-876f-45d7-93cc-4b82e071be47', 'Revenu', 'Revenue', 'Direct customer/sales impact', 'hsl(var(--green))', 'hsl(var(--green) / 0.1)', 'hsl(var(--green) / 0.3)', TRUE, 1, '2025-07-17T07:46:04.382726+00:00', '2025-07-17T08:05:45.747689+00:00');

-- Insert data for projects
TRUNCATE TABLE projects RESTART IDENTITY CASCADE;
INSERT INTO projects (id, name, type, customer_name, project_lead, customer_lead, customer_contact, description, status, created_at, updated_at, start_date, end_date, original_end_date, project_lead_id, customer_lead_id, completed_at, company_name, prd_document_link, priority_order, poc_url, status_changed_at, priority_level, effort_estimate, impact_type, priority_assigned_at, last_reviewed_at, auto_escalated, archived_at, impact_type_id) VALUES ('e0500b35-0dd9-47ec-8d03-141e394259ed', 'Meta Agent Platform', 'internal', '', 'Kavi Koneti ', NULL, '', 'AI Agent platform to generate and execute AI Agents.', 'backlog', '2025-07-16T09:42:06.266509+00:00', '2025-07-17T07:46:04.382726+00:00', '2025-07-13', '2025-08-14', '2025-08-14', 'a9c89b12-c9cb-4727-baec-41493ccb80c7', NULL, NULL, 'TwoDot AI', '', 1000, '', '2025-07-16T09:42:06.266509+00:00', 'P3', 'M', 'Platform', '2025-07-16T09:42:06.266509+00:00', '2025-07-16T09:42:06.266509+00:00', FALSE, NULL, 'a4a2a411-867f-4aa6-a667-e4f28d8f8804');
INSERT INTO projects (id, name, type, customer_name, project_lead, customer_lead, customer_contact, description, status, created_at, updated_at, start_date, end_date, original_end_date, project_lead_id, customer_lead_id, completed_at, company_name, prd_document_link, priority_order, poc_url, status_changed_at, priority_level, effort_estimate, impact_type, priority_assigned_at, last_reviewed_at, auto_escalated, archived_at, impact_type_id) VALUES ('9122f10b-3b3f-47db-8f75-7e95761063bc', 'CRM UI Update', 'internal', '', 'Arjuna Kuraganti', NULL, '', '', 'in-progress', '2025-07-17T06:59:43.84353+00:00', '2025-07-17T07:46:04.382726+00:00', '2025-07-16', '2025-07-17', '2025-07-17', 'ee2f08a6-a525-4937-9795-658cebccfed0', NULL, NULL, 'TwoDot AI', '', 1000, '', '2025-07-17T06:59:43.84353+00:00', 'P0', 'S', 'R&D', '2025-07-17T06:59:43.84353+00:00', '2025-07-17T06:59:43.84353+00:00', FALSE, NULL, '516d941e-fc49-483f-ac76-79b8f944812f');
INSERT INTO projects (id, name, type, customer_name, project_lead, customer_lead, customer_contact, description, status, created_at, updated_at, start_date, end_date, original_end_date, project_lead_id, customer_lead_id, completed_at, company_name, prd_document_link, priority_order, poc_url, status_changed_at, priority_level, effort_estimate, impact_type, priority_assigned_at, last_reviewed_at, auto_escalated, archived_at, impact_type_id) VALUES ('de805d7c-8848-4ec5-9778-bd80a05d4a0b', 'Project Dashboard ', 'internal', '', 'Jackson Tumbridge ', NULL, '', 'A project dashboard that displays all active projects along with their current progress, attached PRDs, and linked POCs. Each project will be shown with a clear status indicator and ordered by priority to help teams focus on the most important work first. The dashboard will provide a quick overview of what’s in motion, what’s completed, and what’s still in planning, making it easy to track progress and ensure alignment across teams.', 'in-progress', '2025-07-15T05:52:04.291704+00:00', '2025-07-17T07:46:04.382726+00:00', '2025-07-10', '2025-07-18', NULL, '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL, NULL, 'TwoDot AI', '', 7, 'https://core-project-pulse.lovable.app/', '2025-07-17T07:04:37.051226+00:00', 'P3', 'M', 'Bug Fix', '2025-07-15T20:09:47.907074+00:00', '2025-07-15T20:09:47.907074+00:00', FALSE, NULL, 'd7b98af1-031b-4aff-8c03-dd09d7b871f4');
INSERT INTO projects (id, name, type, customer_name, project_lead, customer_lead, customer_contact, description, status, created_at, updated_at, start_date, end_date, original_end_date, project_lead_id, customer_lead_id, completed_at, company_name, prd_document_link, priority_order, poc_url, status_changed_at, priority_level, effort_estimate, impact_type, priority_assigned_at, last_reviewed_at, auto_escalated, archived_at, impact_type_id) VALUES ('0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22', 'Transcript Analyzer', 'external', 'Marcus Worrall & Leonie Rothwell', 'Will White ', 'Grant Moyle', '', 'The Meeting Transcript Analysis Agent is an AI-powered tool designed to automate the extraction of customer pain points from meeting transcripts and match them to predefined business solutions (“Problems We Solve”). Built on Google Cloud and integrated with OneDrive, Supabase, and Vertex AI, the system drastically reduces analysis time from hours to minutes while achieving over 80% accuracy. Targeted at Directors and Product Managers, the tool offers a secure React web interface with drag-and-drop upload, scoring dashboards, and rubric management. Key capabilities include NLP-driven theme analysis, multi-factor scoring, and solution recommendations, with a planned 8-week rollout across MVP, enhancement, and scaling phases.', 'not-started', '2025-07-14T03:42:24.661612+00:00', '2025-07-17T07:46:04.382726+00:00', '2025-07-13', '2025-07-30', NULL, '94bcd00f-964e-4a50-adb6-e855be9ad3c0', '4718b85c-5ca1-4d4a-8b42-c221466e2bd4', NULL, 'Sprouta', 'https://docs.google.com/document/d/1ZrQeuLWXw_CJinDwROl_UIJ9i_Jf_os7NpwLO4eAv10/edit?tab=t.0#heading=h.eazeg5qpsb7g', 2, 'https://sprout-transcript-insights.lovable.app/', '2025-07-16T04:15:33.582434+00:00', 'P0', 'XL', 'R&D', '2025-07-16T04:24:27.661676+00:00', '2025-07-16T04:24:27.661676+00:00', FALSE, NULL, '516d941e-fc49-483f-ac76-79b8f944812f');
INSERT INTO projects (id, name, type, customer_name, project_lead, customer_lead, customer_contact, description, status, created_at, updated_at, start_date, end_date, original_end_date, project_lead_id, customer_lead_id, completed_at, company_name, prd_document_link, priority_order, poc_url, status_changed_at, priority_level, effort_estimate, impact_type, priority_assigned_at, last_reviewed_at, auto_escalated, archived_at, impact_type_id) VALUES ('72b99eaf-e3d7-4d3e-910b-8f5e4f206279', 'orbit UI/UX templates and components', 'internal', '', 'Arjuna Kuraganti', NULL, '', 'create the inital figma file to outline the look and feel of the two dot orbit', 'in-progress', '2025-07-16T22:00:06.095637+00:00', '2025-07-17T07:46:04.382726+00:00', '2025-07-16', '2025-07-17', '2025-07-17', 'ee2f08a6-a525-4937-9795-658cebccfed0', NULL, NULL, 'TwoDot AI', '', 1000, '', '2025-07-17T06:06:57.44551+00:00', 'P0', 'S', 'Platform', '2025-07-16T22:01:05.578552+00:00', '2025-07-16T22:01:05.578552+00:00', FALSE, NULL, 'a4a2a411-867f-4aa6-a667-e4f28d8f8804');
INSERT INTO projects (id, name, type, customer_name, project_lead, customer_lead, customer_contact, description, status, created_at, updated_at, start_date, end_date, original_end_date, project_lead_id, customer_lead_id, completed_at, company_name, prd_document_link, priority_order, poc_url, status_changed_at, priority_level, effort_estimate, impact_type, priority_assigned_at, last_reviewed_at, auto_escalated, archived_at, impact_type_id) VALUES ('159d7520-8b08-4cbb-bd05-e91ea85498fd', 'PRD Service', 'internal', '', 'Kavi Koneti ', NULL, '', 'The PRD Management Service is a centralized tool that helps teams at our AI company create, edit, and track product requirement documents in one place. Instead of using scattered tools like Google Docs or Notion, this service provides a consistent format, version control, and collaboration features. It connects directly to our project management tools, making it easy to link PRDs to tasks and track progress. This ensures everyone—from product to engineering—is aligned, speeding up development and reducing miscommunication.', 'backlog', '2025-07-14T07:48:35.704504+00:00', '2025-07-17T07:46:04.382726+00:00', '2025-07-15', '2025-07-20', NULL, 'a9c89b12-c9cb-4727-baec-41493ccb80c7', NULL, NULL, 'TwoDot AI', '', 1, '', '2025-07-16T02:32:03.975963+00:00', 'P0', 'M', 'Bug Fix', '2025-07-16T04:22:55.712262+00:00', '2025-07-16T04:22:55.712262+00:00', FALSE, NULL, 'd7b98af1-031b-4aff-8c03-dd09d7b871f4');
INSERT INTO projects (id, name, type, customer_name, project_lead, customer_lead, customer_contact, description, status, created_at, updated_at, start_date, end_date, original_end_date, project_lead_id, customer_lead_id, completed_at, company_name, prd_document_link, priority_order, poc_url, status_changed_at, priority_level, effort_estimate, impact_type, priority_assigned_at, last_reviewed_at, auto_escalated, archived_at, impact_type_id) VALUES ('da587205-cd0f-4a27-9cba-5f4c4a658427', 'Orbit Setup + CRM Service ', 'internal', '', 'Will White ', NULL, '', 'Should be viewable on internal.dev.twodot.ai

We will develop a dedicated CRM Service to support customer relationship management across the platform. This service will operate as its own backend module, responsible for storing and managing client records, communication logs, pipeline stages, and task assignments. It will expose a secure API for integration with the main web application and other internal services. The CRM Service will be designed for scalability, supporting role-based access, activity tracking, and future integrations with external tools like email and calendar systems.', 'in-progress', '2025-07-14T07:54:57.593347+00:00', '2025-07-17T07:46:04.382726+00:00', '2025-07-09', '2025-07-16', '2025-07-16', '94bcd00f-964e-4a50-adb6-e855be9ad3c0', NULL, NULL, 'TwoDot AI', '', 4, '', '2025-07-16T10:02:58.522579+00:00', 'P0', 'M', 'Bug Fix', '2025-07-16T04:32:57.925644+00:00', '2025-07-16T04:32:57.925644+00:00', FALSE, NULL, 'd7b98af1-031b-4aff-8c03-dd09d7b871f4');
INSERT INTO projects (id, name, type, customer_name, project_lead, customer_lead, customer_contact, description, status, created_at, updated_at, start_date, end_date, original_end_date, project_lead_id, customer_lead_id, completed_at, company_name, prd_document_link, priority_order, poc_url, status_changed_at, priority_level, effort_estimate, impact_type, priority_assigned_at, last_reviewed_at, auto_escalated, archived_at, impact_type_id) VALUES ('90299ea5-9603-4436-b1a4-ccaf3154e391', 'Expense Creation Agent', 'internal', '', 'Kavi Koneti ', NULL, '', 'The Accounts Email Bill Agent for Xero is designed to automate the processing of bill-related emails sent to `<EMAIL>`. It parses PDF and image attachments, extracts key invoice data, applies Australian tax and multi-currency rules, infers appropriate account codes, and drafts bills in Xero. The agent uses learning from user corrections over time and integrates with Orbit’s internal dashboard for visibility, feedback, and full auditability of each step.

Targeted at Twodot’s internal accounts team, the agent supports both real-time and historical email ingestion, emphasizes accuracy over volume (<100 emails/month), and ensures compliance with audit and security standards. It leverages existing Orbit and Xero systems, with core success metrics tied to high parsing accuracy, low review rates, and significant time savings in manual processing.', 'not-started', '2025-07-15T23:34:23.981896+00:00', '2025-07-17T07:46:04.382726+00:00', '2025-07-19', '2025-07-30', '2025-07-30', 'a9c89b12-c9cb-4727-baec-41493ccb80c7', NULL, NULL, 'TwoDot AI', 'https://drive.google.com/file/d/1mxL6B7Mc8FH5K8kmzdAWL950w74erj7n/view?usp=drive_link', 14, '', '2025-07-15T23:34:23.981896+00:00', 'P4', 'M', 'Platform', '2025-07-15T23:50:54.442244+00:00', '2025-07-15T23:50:54.442244+00:00', FALSE, NULL, 'a4a2a411-867f-4aa6-a667-e4f28d8f8804');
INSERT INTO projects (id, name, type, customer_name, project_lead, customer_lead, customer_contact, description, status, created_at, updated_at, start_date, end_date, original_end_date, project_lead_id, customer_lead_id, completed_at, company_name, prd_document_link, priority_order, poc_url, status_changed_at, priority_level, effort_estimate, impact_type, priority_assigned_at, last_reviewed_at, auto_escalated, archived_at, impact_type_id) VALUES ('162605f2-9da4-4dcc-85e7-131018b5418a', 'Candidate Assessor ', 'internal', '', 'Jackson Tumbridge ', NULL, '', 'The Candidate Assessor is an automated AI-powered system that processes job applications sent to the company’s Careers email. When a candidate submits their CV and cover letter via email, an n8n workflow captures the incoming message, extracts the email body and CV attachment, and logs the metadata. The workflow then uses AI to determine which job role the candidate is applying for by analyzing the language in the email and cross-referencing it with open roles in a Supabase database. If a matching role is found, the system retrieves the corresponding scoring rubric and passes the candidate’s CV and email content through an AI model, which evaluates the application against key criteria such as technical fit, communication skills, and relevant experience. The model returns a score out of 100, which is stored alongside the candidate’s information and resume in the Supabase backend. The hiring team can then review candidates through a clean, user-friendly frontend built using Lovable.dev, which displays applications, scores, and rubrics in a structured and interactive interface. This end-to-end workflow streamlines candidate screening while ensuring consistent, rubric-based evaluations at scale.', 'in-progress', '2025-07-17T02:29:26.577145+00:00', '2025-07-17T07:46:04.382726+00:00', '2025-07-09', '2025-07-21', '2025-07-21', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL, NULL, 'TwoDot AI', '', 1000, 'https://preview--core-project-pulse.lovable.app/projects/new', '2025-07-17T02:29:26.577145+00:00', 'P2', 'M', 'Platform', '2025-07-17T02:34:19.693161+00:00', '2025-07-17T02:34:19.693161+00:00', FALSE, NULL, 'a4a2a411-867f-4aa6-a667-e4f28d8f8804');
INSERT INTO projects (id, name, type, customer_name, project_lead, customer_lead, customer_contact, description, status, created_at, updated_at, start_date, end_date, original_end_date, project_lead_id, customer_lead_id, completed_at, company_name, prd_document_link, priority_order, poc_url, status_changed_at, priority_level, effort_estimate, impact_type, priority_assigned_at, last_reviewed_at, auto_escalated, archived_at, impact_type_id) VALUES ('b04eba31-b738-40f7-9ba8-eed12d725ff2', 'Supply Chain Intel Agent ', 'external', 'Mark Dogan ', 'Kavi Koneti ', 'Grant Moyle', '', 'The ISG Supply Chain Intelligence Agent automates the ingestion and processing of supply chain data from emails, PDFs, and manual entries to replace a manual, reactive system with a proactive, rule-based exception monitoring platform. Built on Google Cloud with n8n, Supabase, and a React frontend, the agent identifies delays, updates dashboards in real time, and automates customer communications. Key features include exception detection, human-in-the-loop escalation, real-time container tracking, and business intelligence dashboards. Integration with Microsoft 365, SharePoint, Xero, and Fishbowl supports comprehensive data flow, with a phased rollout planned over 8 weeks to ensure reliability, accuracy, and user adoption.', 'backlog', '2025-07-14T03:37:32.171551+00:00', '2025-07-17T07:46:04.382726+00:00', '2025-07-13', '2025-07-31', '2025-07-31', 'a9c89b12-c9cb-4727-baec-41493ccb80c7', '4718b85c-5ca1-4d4a-8b42-c221466e2bd4', NULL, 'Integrated Supply Group (ISG)', 'https://docs.google.com/document/d/1yKH90hbz7z4BgzUurmpvQMtFvLbmrkMhCTqt0cCvYMk/edit?tab=t.0', 11, NULL, '2025-07-15T07:08:57.770426+00:00', 'P3', 'M', 'Bug Fix', '2025-07-15T20:09:47.907074+00:00', '2025-07-15T20:09:47.907074+00:00', FALSE, NULL, 'd7b98af1-031b-4aff-8c03-dd09d7b871f4');
INSERT INTO projects (id, name, type, customer_name, project_lead, customer_lead, customer_contact, description, status, created_at, updated_at, start_date, end_date, original_end_date, project_lead_id, customer_lead_id, completed_at, company_name, prd_document_link, priority_order, poc_url, status_changed_at, priority_level, effort_estimate, impact_type, priority_assigned_at, last_reviewed_at, auto_escalated, archived_at, impact_type_id) VALUES ('e3ead8ad-944e-469a-88f8-02c53ece4734', 'AR Automation Agent ', 'external', 'Mark Dogan ', 'Kavi Koneti ', 'Grant Moyle', '0414 343 237', 'The AR Automation Agent is an AI-powered system designed to streamline accounts receivable processes by automating monthly invoice outreach, capturing customer payment intentions via interactive email buttons, and escalating unresolved cases based on business rules. Integrated with Xero, Microsoft 365, and optionally Fishbowl, the agent reduces manual follow-up time by 80% and provides real-time visibility through a live dashboard. Built on Google Cloud using n8n, Supabase, and Microsoft Graph API, it supports configurable templates, escalation thresholds, and follow-up sequences, with a scalable architecture capable of handling up to 100,000 customers. A phased rollout ensures seamless integration, high adoption, and measurable improvements in AR efficiency and response tracking.', 'not-started', '2025-07-14T03:35:33.271595+00:00', '2025-07-17T07:46:04.382726+00:00', '2025-07-20', '2025-07-31', NULL, 'a9c89b12-c9cb-4727-baec-41493ccb80c7', '4718b85c-5ca1-4d4a-8b42-c221466e2bd4', NULL, 'Integrated Supply Group (ISG)', 'https://docs.google.com/document/d/1MKvytd5EKM0dkkm11oltJ-nnLNl9hxHyhSGyGDORocE/edit?tab=t.0', 5, '', '2025-07-15T07:08:57.770426+00:00', 'P2', 'L', 'Revenue', '2025-07-16T04:26:40.764238+00:00', '2025-07-16T04:26:40.764238+00:00', FALSE, NULL, 'efd7ddfd-876f-45d7-93cc-4b82e071be47');

-- Insert data for tasks
TRUNCATE TABLE tasks RESTART IDENTITY CASCADE;
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('53f9b90a-5e5f-445c-8bdb-5a053fbc889e', 'b04eba31-b738-40f7-9ba8-eed12d725ff2', 'Fishbowl MCP Creation ', 'Model Context Protocol (MCP) server for the Fishbowl Inventory system to enable AI agents to interact with inventory, order, and shipment data programmatically. The server will act as an interface layer between Fishbowl’s APIs and AI-driven agents, facilitating structured queries, automated decision-making, and workflow execution within existing infrastructure. Key capabilities include inventory lookups, order status checks, shipment tracking, and low-stock alerts, all accessible via standardized MCP endpoints. Built on Google Cloud with n8n orchestration and secured via OAuth, the server will support integrations with Supabase, Microsoft 365, and other business systems. The goal is to improve inventory visibility, reduce manual query overhead, and enable intelligent automation for supply chain operations.', 'Kavi Koneti ', '2025-07-14', 'to-do', '2025-07-16T21:12:16.275503+00:00', '2025-07-16T21:12:16.275503+00:00', 'a9c89b12-c9cb-4727-baec-41493ccb80c7', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('49c601c3-853e-4675-86be-41d4ac86ad0e', 'da587205-cd0f-4a27-9cba-5f4c4a658427', 'Deploy Orbit + CRM in working state to GCP', '', 'Will White ', NULL, 'in-progress', '2025-07-16T01:06:31.12433+00:00', '2025-07-16T01:06:50.713926+00:00', '94bcd00f-964e-4a50-adb6-e855be9ad3c0', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('ebe3de8d-5380-48db-b3a4-c41ef15c6753', 'da587205-cd0f-4a27-9cba-5f4c4a658427', 'Add Google Account Logins', '', 'Will White ', NULL, 'to-do', '2025-07-16T01:07:28.545082+00:00', '2025-07-16T01:07:28.545082+00:00', '94bcd00f-964e-4a50-adb6-e855be9ad3c0', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('e129b68c-e3d4-4dc6-b999-8544babbfd6c', 'da587205-cd0f-4a27-9cba-5f4c4a658427', 'Clean up and document Deployment process', '', 'Will White ', NULL, 'to-do', '2025-07-16T01:07:49.222152+00:00', '2025-07-16T01:07:49.222152+00:00', '94bcd00f-964e-4a50-adb6-e855be9ad3c0', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('41ca3fc9-0cda-417f-9ded-f641475a6997', 'de805d7c-8848-4ec5-9778-bd80a05d4a0b', 'Define Dashboard Requirements ', '', 'Jackson Tumbridge ', NULL, 'done', '2025-07-16T03:21:26.175036+00:00', '2025-07-16T03:21:26.175036+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('930d2320-f999-4068-912e-35ef72d0cc1f', 'de805d7c-8848-4ec5-9778-bd80a05d4a0b', 'Add Projects from Sheets ', 'Add all jobs with appropriate leads from the google sheet Kavi and Will made. ', 'Jackson Tumbridge ', NULL, 'done', '2025-07-16T03:23:48.011952+00:00', '2025-07-16T03:23:48.011952+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('9ec4650c-06f5-474a-a45d-c7dc4974dfa4', 'de805d7c-8848-4ec5-9778-bd80a05d4a0b', 'implementing start date, end date ', 'having color coded project''s to see what is close to deadline and what isn''t incorporated into statuses of each project.', 'Jackson Tumbridge ', NULL, 'done', '2025-07-16T03:59:02.41148+00:00', '2025-07-16T03:59:05.279862+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('a93cd5ce-c743-4e6f-b495-e4d45ab357ed', 'de805d7c-8848-4ec5-9778-bd80a05d4a0b', 'Talk with team', 'ask the team their opinion and add what they think would be good for them ', 'Jackson Tumbridge ', NULL, 'done', '2025-07-16T04:01:35.572644+00:00', '2025-07-16T04:01:35.572644+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('7fb428a6-9e80-42a7-833b-f3a9c1cfb276', '0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22', 'Mvp internal delivery ', '', 'Will White ', '2025-07-17', 'to-do', '2025-07-16T04:25:59.318566+00:00', '2025-07-16T04:25:59.318566+00:00', '94bcd00f-964e-4a50-adb6-e855be9ad3c0', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('3b93a105-9f77-4457-a37e-0b236a644d58', 'de805d7c-8848-4ec5-9778-bd80a05d4a0b', 'Deploy ', 'So everyone can input their projects they are working on and enter their tasks. ', 'Jackson Tumbridge ', NULL, 'done', '2025-07-16T04:02:47.929927+00:00', '2025-07-16T04:29:00.775984+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('90b9dc51-0a09-4258-8df2-57ba967190ac', 'de805d7c-8848-4ec5-9778-bd80a05d4a0b', 'Make done tasks green background and put an option to not show done tasks', '', 'Jackson Tumbridge ', '2025-07-15', 'done', '2025-07-16T04:30:19.77468+00:00', '2025-07-16T07:52:08.223709+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('5f5a8665-7ec7-4a3d-a914-e704281687d8', 'de805d7c-8848-4ec5-9778-bd80a05d4a0b', 'create archive function and change toggle to show archive', '', 'Jackson Tumbridge ', '2025-07-16', 'done', '2025-07-16T21:13:57.648147+00:00', '2025-07-17T00:25:05.389488+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('85c5c296-f2b1-42fa-b0f1-e58c7ca96514', 'de805d7c-8848-4ec5-9778-bd80a05d4a0b', 'add the ability to add priority details on creation of project', '', 'Jackson Tumbridge ', '2025-07-16', 'done', '2025-07-16T22:00:43.190871+00:00', '2025-07-17T00:31:10.456682+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('7653456d-c8b3-4351-9ac6-c35184ee90ca', 'de805d7c-8848-4ec5-9778-bd80a05d4a0b', 'Clean up organisation and overall UI', '', 'Jackson Tumbridge ', '2025-07-16', 'done', '2025-07-17T00:34:52.236466+00:00', '2025-07-17T00:34:52.236466+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('3044e5a8-0780-485c-9e11-b2432c4dd7ac', 'de805d7c-8848-4ec5-9778-bd80a05d4a0b', 'add group by lead', '', 'Jackson Tumbridge ', '2025-07-16', 'done', '2025-07-16T22:02:11.795317+00:00', '2025-07-17T00:55:01.039586+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('d68dc239-c019-4621-a90f-eb1d162ac526', 'da587205-cd0f-4a27-9cba-5f4c4a658427', 'Web (SPA) ', 'Web Application (SPA)', 'Kavi Koneti ', NULL, 'done', '2025-07-17T01:09:04.196026+00:00', '2025-07-17T01:09:04.196026+00:00', '94bcd00f-964e-4a50-adb6-e855be9ad3c0', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('2c780c93-6d56-42f8-9e0f-ac184b4deacc', 'da587205-cd0f-4a27-9cba-5f4c4a658427', 'Gateway Service ', 'The Gateway Service will act as the centralized entry point for all traffic across the AI platform, handling authentication, request routing, rate limiting, and logging. It will provide a consistent interface for clients and services, improve security by enforcing unified access controls, and simplify integration with internal tools and external APIs. This service ensures scalability, observability, and reliability as the platform grows.', 'Kavi Koneti ', NULL, 'done', '2025-07-17T01:15:51.06267+00:00', '2025-07-17T01:15:51.06267+00:00', 'a9c89b12-c9cb-4727-baec-41493ccb80c7', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('bdd7677d-6d6d-41a4-89f3-8fceda414f48', 'da587205-cd0f-4a27-9cba-5f4c4a658427', 'Extended Web to feature CRM ', 'Extend the existing web application to include a CRM dashboard as a new feature. This dashboard will provide a centralized view of customer interactions, status updates, and communication history. It will be designed with a clean, user-friendly interface and integrate with existing user and project data. The CRM module will support filtering, search, tagging, and activity tracking, and will be built using the same tech stack as the core app to ensure seamless integration. Role-based access will control visibility, and all CRM data will be stored securely in the platform’s primary database.', 'Will White ', NULL, 'done', '2025-07-17T01:15:59.415004+00:00', '2025-07-17T01:15:59.415004+00:00', '94bcd00f-964e-4a50-adb6-e855be9ad3c0', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('d48a892f-4736-4ba3-af47-10e3e6ad5a3a', 'da587205-cd0f-4a27-9cba-5f4c4a658427', 'Database Setup', 'Centralized PostgreSQL database to support our AI platform’s core services. This database will store structured data such as user activity, model configurations, and product metadata. It will include role-based access control, daily automated backups with 30-day retention, and be provisioned using standardized schema templates for consistency across services. The setup will support separate environments for development, staging, and production, with monitoring in place for performance and cost tracking. The goal is to ensure a secure, scalable, and easy-to-manage foundation for all data-driven features on the platform.', 'Kavi Koneti ', NULL, 'done', '2025-07-17T01:16:05.839093+00:00', '2025-07-17T01:16:05.839093+00:00', 'a9c89b12-c9cb-4727-baec-41493ccb80c7', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('2102b468-ed9f-47d7-9f78-285a2c811247', 'da587205-cd0f-4a27-9cba-5f4c4a658427', 'Extend Gateway For CRM API spec', 'Extend the existing Gateway Service to support the new CRM API, enabling secure and consistent access to CRM-related endpoints. This includes routing requests to the CRM service, enforcing authentication via API keys or JWT, and applying rate limiting per client. The CRM API spec will follow RESTful conventions and include endpoints for managing contacts, companies, interactions, and pipelines. Gateway logging and monitoring will capture usage metrics and errors for CRM traffic, ensuring visibility and reliability across the full request lifecycle.', 'Will White ', NULL, 'done', '2025-07-17T01:16:12.977615+00:00', '2025-07-17T01:16:12.977615+00:00', '94bcd00f-964e-4a50-adb6-e855be9ad3c0', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('8c0819d0-f414-403b-91df-9a5382a33629', 'de805d7c-8848-4ec5-9778-bd80a05d4a0b', 'not having the ability to covert to task or make edits to a project that in archive  ', '', 'Jackson Tumbridge ', NULL, 'done', '2025-07-17T01:00:46.186649+00:00', '2025-07-17T01:16:17.996497+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('d938f07a-f3f5-4206-bad7-5bac6211d552', 'da587205-cd0f-4a27-9cba-5f4c4a658427', 'Extended DB for CRM ', 'extend the existing PostgreSQL database to support the CRM service by adding new tables for contacts, companies, interactions, and pipeline stages. These tables will be relational and linked to existing user and project data. The schema will support activity tracking, custom fields, and be managed through versioned migrations. All CRM data will be included in existing backup and monitoring processes.', 'Will White ', '2025-07-30', 'done', '2025-07-17T01:29:30.403247+00:00', '2025-07-17T01:29:35.992318+00:00', '94bcd00f-964e-4a50-adb6-e855be9ad3c0', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('15fc06f2-efee-4e88-a5b1-1943bea6d37e', '162605f2-9da4-4dcc-85e7-131018b5418a', 'Design Frontend with Lovable', '', 'Jackson Tumbridge ', NULL, 'done', '2025-07-17T02:35:39.412503+00:00', '2025-07-17T02:35:39.412503+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('9b43e939-ae6a-4520-829d-8d3ba3e99fce', '162605f2-9da4-4dcc-85e7-131018b5418a', 'Set Up Supabase Backend', '', 'Jackson Tumbridge ', NULL, 'done', '2025-07-17T02:35:20.077067+00:00', '2025-07-17T02:35:41.12302+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('89075347-65f0-44a2-96ed-c0e5f056b117', '162605f2-9da4-4dcc-85e7-131018b5418a', 'Create Email Ingestion Workflow in n8n', '', 'Jackson Tumbridge ', NULL, 'done', '2025-07-17T02:39:30.774856+00:00', '2025-07-17T02:39:30.774856+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('19ed9f27-2f38-47d6-8862-0700129f7c77', '162605f2-9da4-4dcc-85e7-131018b5418a', 'Build AI Role Classifier (Prompt + Node)', '', 'Jackson Tumbridge ', NULL, 'done', '2025-07-17T02:39:43.921895+00:00', '2025-07-17T02:39:43.921895+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('70527071-188a-492e-b195-f825f8a11e35', '162605f2-9da4-4dcc-85e7-131018b5418a', 'Implement CV Scoring via AI & Rubric', '', 'Jackson Tumbridge ', NULL, 'done', '2025-07-17T02:40:02.728997+00:00', '2025-07-17T02:40:02.728997+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('b47fcca8-2200-403e-bc4b-43db81f41d4e', '162605f2-9da4-4dcc-85e7-131018b5418a', 'Set Up Frontend Score Display', '', 'Jackson Tumbridge ', NULL, 'done', '2025-07-17T02:40:18.812398+00:00', '2025-07-17T02:40:18.812398+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('fc622004-1396-4128-aaf1-a9b0bfe31e01', '162605f2-9da4-4dcc-85e7-131018b5418a', 'Test with sample data ', '', 'Jackson Tumbridge ', NULL, 'in-progress', '2025-07-17T02:43:05.175199+00:00', '2025-07-17T02:43:09.091026+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('62a81c58-13de-4e65-8864-5767474ef589', '162605f2-9da4-4dcc-85e7-131018b5418a', 'fix marking rubric sections issue ', '', 'Jackson Tumbridge ', NULL, 'in-progress', '2025-07-17T02:43:51.304703+00:00', '2025-07-17T02:43:56.007949+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('2e0ae0f3-bcfb-45e4-a626-19b936ebe3ed', 'de805d7c-8848-4ec5-9778-bd80a05d4a0b', 'add list titles on grouping', 'when the list is grouped - you should still be able to see the column titles - currently they are not showing in group view', 'Jackson Tumbridge ', '2025-07-16', 'to-do', '2025-07-17T07:10:35.042435+00:00', '2025-07-17T07:10:35.042435+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('acc7c5e4-31fe-435d-9971-49692572b93f', 'de805d7c-8848-4ec5-9778-bd80a05d4a0b', 'create impact type table', 'change the imapct types to a table that can be changed dynamically. id, impact type, description.
-then use the able data to populate the impact type', 'Jackson Tumbridge ', '2025-07-16', 'done', '2025-07-17T07:04:59.400231+00:00', '2025-07-17T08:05:52.445245+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);
INSERT INTO tasks (id, project_id, name, description, assignee, due_date, status, created_at, updated_at, assignee_id, completed_at) VALUES ('eb2d97ae-4287-4071-bf9a-9e8542d6b6db', 'de805d7c-8848-4ec5-9778-bd80a05d4a0b', 'retain session view', 'when sorting, filtering or grouping the list. if the user clicks into a project - when they go back the list filter, sort, group should survive - at the moment it clears all filters', 'Jackson Tumbridge ', '2025-07-16', 'done', '2025-07-17T07:06:41.89828+00:00', '2025-07-17T08:19:49.702004+00:00', '38e0aa56-df19-40fe-b02e-0aa7d07e76cf', NULL);

-- No data to insert for sub_tasks
-- Insert data for priority_history
TRUNCATE TABLE priority_history RESTART IDENTITY CASCADE;
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('09692463-fa99-4f55-a9e8-8a02e7906ea8', '0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22', 'P3', 'P0', NULL, NULL, FALSE, '2025-07-15T22:36:16.797916+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('9b425a2a-5538-4b32-a3ef-87fc983eb775', '0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22', 'P0', 'P1', NULL, NULL, FALSE, '2025-07-15T22:36:23.801807+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('007f149b-e95a-4a4e-9daf-555eb35f8957', '0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22', 'P1', 'P2', NULL, NULL, FALSE, '2025-07-15T22:36:26.660837+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('8b384180-bdee-4947-a107-7ae843e9f67f', '0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22', 'P2', 'P3', NULL, NULL, FALSE, '2025-07-15T22:36:28.872894+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('b5e99a1f-43a0-4e2d-aca2-db07b0faf75d', '0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22', 'P3', 'P4', NULL, NULL, FALSE, '2025-07-15T22:36:31.012674+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('20dd83d7-2fad-4b36-92ac-64c386b38edd', '0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22', 'P4', 'P0', NULL, NULL, FALSE, '2025-07-15T22:39:09.865854+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('f4fa2790-af12-4de5-9299-4e47bad8331b', '0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22', 'P0', 'P1', NULL, NULL, FALSE, '2025-07-15T22:58:37.394277+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('ebe73692-e71a-4538-a5ee-7fba93b83a80', '90299ea5-9603-4436-b1a4-ccaf3154e391', 'P3', 'P1', NULL, NULL, FALSE, '2025-07-15T23:34:32.777455+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('654b3f0b-1854-4a21-a7f9-0e61da17a172', '90299ea5-9603-4436-b1a4-ccaf3154e391', 'P1', 'P2', NULL, NULL, FALSE, '2025-07-15T23:34:35.435711+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('6b742f5f-67d3-41e9-af25-effb1924588f', 'e3ead8ad-944e-469a-88f8-02c53ece4734', 'P3', 'P1', NULL, NULL, FALSE, '2025-07-15T23:40:29.490544+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('48242702-eb19-4805-a567-e43bdecf01b1', '90299ea5-9603-4436-b1a4-ccaf3154e391', 'P2', 'P4', NULL, NULL, FALSE, '2025-07-15T23:50:54.442244+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('5dd1226c-ef8c-4734-ac89-811d5ddd929b', '159d7520-8b08-4cbb-bd05-e91ea85498fd', 'P3', 'P1', NULL, NULL, FALSE, '2025-07-16T00:47:10.623367+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('c719c2af-e494-4436-98bd-be0097c5d561', '159d7520-8b08-4cbb-bd05-e91ea85498fd', 'P1', 'P0', NULL, NULL, FALSE, '2025-07-16T04:22:55.712262+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('0ce20269-482d-405e-a903-ba098587a5a2', '0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22', 'P1', 'P0', NULL, NULL, FALSE, '2025-07-16T04:24:27.661676+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('bac8e519-9f60-4e01-9eab-6891c284d28e', 'e3ead8ad-944e-469a-88f8-02c53ece4734', 'P1', 'P2', NULL, NULL, FALSE, '2025-07-16T04:26:40.764238+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('80266ce2-5b23-47fe-896e-5db1b1b8ffa8', 'da587205-cd0f-4a27-9cba-5f4c4a658427', 'P3', 'P0', NULL, NULL, FALSE, '2025-07-16T04:32:57.925644+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('dd9e2654-7e3e-4cc9-8368-97d86f67ff1e', '72b99eaf-e3d7-4d3e-910b-8f5e4f206279', 'P3', 'P0', NULL, NULL, FALSE, '2025-07-16T22:01:05.578552+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('823d78a8-d31f-4522-92b3-defcdc415388', '162605f2-9da4-4dcc-85e7-131018b5418a', 'P3', 'P1', NULL, NULL, FALSE, '2025-07-17T02:34:17.22043+00:00');
INSERT INTO priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) VALUES ('90346bfd-aade-4eb1-9a54-7c472d8848a6', '162605f2-9da4-4dcc-85e7-131018b5418a', 'P1', 'P2', NULL, NULL, FALSE, '2025-07-17T02:34:19.693161+00:00');

-- Insert data for priority_rules
TRUNCATE TABLE priority_rules RESTART IDENTITY CASCADE;
INSERT INTO priority_rules (id, from_priority, to_priority, max_days, is_active, created_at, updated_at) VALUES ('27dceb17-89ff-4508-8f88-b9fe8bff8402', 'P2', 'P1', 14, TRUE, '2025-07-15T20:09:47.907074+00:00', '2025-07-15T20:09:47.907074+00:00');
INSERT INTO priority_rules (id, from_priority, to_priority, max_days, is_active, created_at, updated_at) VALUES ('21a14967-a5e4-4a21-98cc-04107feb4ea6', 'P3', 'P2', 28, TRUE, '2025-07-15T20:09:47.907074+00:00', '2025-07-15T20:09:47.907074+00:00');

-- No data to insert for project_integrations

SET session_replication_role = DEFAULT;
