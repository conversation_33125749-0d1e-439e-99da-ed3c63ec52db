version: '3.8'

services:
  # PostgreSQL 15 Database
  postgres:
    image: postgres:15-alpine
    container_name: core-project-pulse-db
    environment:
      POSTGRES_DB: core_project_pulse
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init:/docker-entrypoint-initdb.d
      - ./backend/database/backups:/backups
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d core_project_pulse"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis for Caching & Sessions
  redis:
    image: redis:7-alpine
    container_name: core-project-pulse-redis
    command: redis-server --appendonly yes --requirepass redis123
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./backend/database/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  # pgAdmin for Database Management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: core-project-pulse-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    ports:
      - "8081:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
      - ./backend/database/pgadmin-servers.json:/pgadmin4/servers.json
    networks:
      - app-network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
