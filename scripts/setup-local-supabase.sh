#!/bin/bash

# Setup Local Supabase Development Environment
# This script will:
# 1. Start local Supabase
# 2. Run migrations
# 3. Import exported data
# 4. Verify setup

set -e

echo "🚀 Setting up local Supabase development environment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop and try again."
    echo "📖 Install Docker Desktop: https://docs.docker.com/desktop"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "supabase/config.toml" ]; then
    echo "❌ Please run this script from the core-project-pulse directory"
    exit 1
fi

echo "🐳 Starting local Supabase..."
npx supabase start

echo "📊 Running database migrations..."
npx supabase db reset --linked=false

echo "📥 Importing exported data..."
if [ -f "supabase/seed/remote-data.sql" ]; then
    npx supabase db reset --linked=false
    echo "✅ Data imported successfully"
else
    echo "⚠️  No exported data found. Run 'npm run export-data' first to export remote data."
fi

echo "🔍 Verifying setup..."
npx supabase status

echo ""
echo "✅ Local Supabase setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Copy .env.local to .env to use local Supabase"
echo "2. Run 'npm run dev:local' to start development with local Supabase"
echo "3. Access Supabase Studio at: http://localhost:54323"
echo ""
echo "🔧 Useful commands:"
echo "  npm run supabase:start  - Start local Supabase"
echo "  npm run supabase:stop   - Stop local Supabase"
echo "  npm run supabase:reset  - Reset local database"
echo "  npm run dev:local       - Start dev server with local Supabase"
echo ""
