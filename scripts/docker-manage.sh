#!/bin/bash

# Core Project Pulse Docker Management Script
# Usage: ./scripts/docker-manage.sh [start|stop|restart|reset|logs|status]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker Desktop first."
        exit 1
    fi
}

# Start services
start_services() {
    print_status "Starting Core Project Pulse services..."
    docker-compose up -d
    
    print_status "Waiting for services to be healthy..."
    sleep 10
    
    # Check service health
    if docker-compose ps | grep -q "Up (healthy)"; then
        print_success "Services started successfully!"
        print_status "Access points:"
        echo "  📊 pgAdmin: http://localhost:8080 (<EMAIL> / admin123)"
        echo "  🗄️  PostgreSQL: localhost:5432 (postgres / postgres)"
        echo "  🔴 Redis: localhost:6379 (password: redis123)"
    else
        print_warning "Some services may not be fully ready yet. Check logs with: ./scripts/docker-manage.sh logs"
    fi
}

# Stop services
stop_services() {
    print_status "Stopping Core Project Pulse services..."
    docker-compose down
    print_success "Services stopped successfully!"
}

# Restart services
restart_services() {
    print_status "Restarting Core Project Pulse services..."
    docker-compose restart
    print_success "Services restarted successfully!"
}

# Reset services (remove volumes)
reset_services() {
    print_warning "This will remove all data! Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_status "Resetting Core Project Pulse services..."
        docker-compose down -v
        docker-compose up -d
        print_success "Services reset successfully!"
    else
        print_status "Reset cancelled."
    fi
}

# Show logs
show_logs() {
    if [ -n "$2" ]; then
        docker-compose logs -f "$2"
    else
        docker-compose logs -f
    fi
}

# Show status
show_status() {
    print_status "Core Project Pulse Services Status:"
    docker-compose ps
    echo ""
    print_status "Resource Usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
}

# Main script logic
case "$1" in
    start)
        check_docker
        start_services
        ;;
    stop)
        check_docker
        stop_services
        ;;
    restart)
        check_docker
        restart_services
        ;;
    reset)
        check_docker
        reset_services
        ;;
    logs)
        check_docker
        show_logs "$@"
        ;;
    status)
        check_docker
        show_status
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|reset|logs [service]|status}"
        echo ""
        echo "Commands:"
        echo "  start   - Start all services"
        echo "  stop    - Stop all services"
        echo "  restart - Restart all services"
        echo "  reset   - Reset all services (removes data!)"
        echo "  logs    - Show logs (optionally for specific service)"
        echo "  status  - Show service status and resource usage"
        echo ""
        echo "Examples:"
        echo "  $0 start"
        echo "  $0 logs postgres"
        echo "  $0 status"
        exit 1
        ;;
esac
