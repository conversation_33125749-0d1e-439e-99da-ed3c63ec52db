/**
 * Authentication Context
 * Manages user authentication state and Google OAuth flow
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useToast } from '@/hooks/use-toast';

interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  avatarUrl?: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: () => void;
  logout: () => void;
  refreshToken: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api/v1';

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  const isAuthenticated = !!user;

  // Get stored tokens
  const getAccessToken = () => localStorage.getItem('accessToken');
  const getRefreshToken = () => localStorage.getItem('refreshToken');
  const setTokens = (accessToken: string, refreshToken: string) => {
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', refreshToken);
  };
  const clearTokens = () => {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  };

  // API call helper with token
  const apiCall = async (endpoint: string, options: RequestInit = {}) => {
    const token = getAccessToken();
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Network error' }));
      throw new Error(error.message || 'API call failed');
    }

    return response.json();
  };

  // Refresh access token
  const refreshToken = async () => {
    try {
      const refreshTokenValue = getRefreshToken();
      if (!refreshTokenValue) {
        throw new Error('No refresh token available');
      }

      const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken: refreshTokenValue }),
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const data = await response.json();
      const { user: userData, tokens } = data.data;

      setTokens(tokens.accessToken, tokens.refreshToken);
      setUser(userData);
    } catch (error) {
      console.error('Token refresh failed:', error);
      clearTokens();
      setUser(null);
      throw error;
    }
  };

  // Get current user
  const getCurrentUser = async () => {
    try {
      const data = await apiCall('/auth/me');
      setUser(data.data.user);
    } catch (error) {
      console.error('Get current user failed:', error);
      // Try to refresh token
      try {
        await refreshToken();
      } catch (refreshError) {
        clearTokens();
        setUser(null);
      }
    }
  };

  // Login with Google OAuth
  const login = () => {
    window.location.href = `${API_BASE_URL}/auth/google`;
  };

  // Logout
  const logout = async () => {
    try {
      if (getAccessToken()) {
        await apiCall('/auth/logout', { method: 'POST' });
      }
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      clearTokens();
      setUser(null);
      toast({
        title: 'Logged out',
        description: 'You have been successfully logged out.',
      });
    }
  };

  // Handle OAuth callback
  useEffect(() => {
    const handleOAuthCallback = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const token = urlParams.get('token');
      const refresh = urlParams.get('refresh');

      if (token && refresh) {
        setTokens(token, refresh);
        // Clear URL parameters
        window.history.replaceState({}, document.title, window.location.pathname);
        // Get user data
        getCurrentUser();
        toast({
          title: 'Welcome!',
          description: 'You have been successfully logged in.',
        });
      }
    };

    // Check if we're on the OAuth callback route
    if (window.location.pathname === '/auth/callback') {
      handleOAuthCallback();
    }
  }, []);

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true);
      
      const token = getAccessToken();
      if (token) {
        try {
          await getCurrentUser();
        } catch (error) {
          console.error('Auth initialization failed:', error);
        }
      }
      
      setIsLoading(false);
    };

    // Don't initialize if we're handling OAuth callback
    if (window.location.pathname !== '/auth/callback') {
      initializeAuth();
    } else {
      setIsLoading(false);
    }
  }, []);

  // Auto-refresh token before expiration
  useEffect(() => {
    if (!user) return;

    const interval = setInterval(async () => {
      try {
        await refreshToken();
      } catch (error) {
        console.error('Auto token refresh failed:', error);
      }
    }, 14 * 60 * 1000); // Refresh every 14 minutes (token expires in 15 minutes)

    return () => clearInterval(interval);
  }, [user]);

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    refreshToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
