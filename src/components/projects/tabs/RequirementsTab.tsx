import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>List, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';
import { Plus, FileText, Users, CheckCircle2, Clock, AlertCircle } from 'lucide-react';
import { ProjectWithSDLCDetails, Requirement, UserStory } from '@/types/project';

interface RequirementsTabProps {
  project: ProjectWithSDLCDetails;
  isArchived?: boolean;
}

export function RequirementsTab({ project, isArchived = false }: RequirementsTabProps) {
  const [activeSubTab, setActiveSubTab] = useState('requirements');
  
  const requirements = project.requirements || [];
  const userStories = project.user_stories || [];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
      case 'done':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'review':
      case 'in-progress':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'implemented':
      case 'ready':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'tested':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case 'draft':
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
      case 'done':
        return <CheckCircle2 className="h-3 w-3" />;
      case 'review':
      case 'in-progress':
        return <Clock className="h-3 w-3" />;
      case 'draft':
      default:
        return <AlertCircle className="h-3 w-3" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'must-have':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'should-have':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'could-have':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'wont-have':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Requirements & User Stories</h3>
          <p className="text-sm text-muted-foreground">
            Manage project requirements, user stories, and acceptance criteria
          </p>
        </div>
        {!isArchived && (
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Requirement
            </Button>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add User Story
            </Button>
          </div>
        )}
      </div>

      {/* Sub-tabs */}
      <Tabs value={activeSubTab} onValueChange={setActiveSubTab}>
        <TabsList>
          <TabsTrigger value="requirements" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Requirements ({requirements.length})
          </TabsTrigger>
          <TabsTrigger value="user-stories" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            User Stories ({userStories.length})
          </TabsTrigger>
        </TabsList>

        {/* Requirements Content */}
        <TabsContent value="requirements" className="space-y-4">
          {requirements.length > 0 ? (
            <div className="space-y-4">
              {requirements.map((requirement) => (
                <Card key={requirement.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-base">{requirement.title}</CardTitle>
                        <p className="text-sm text-muted-foreground mt-1">
                          {requirement.description}
                        </p>
                      </div>
                      <div className="flex flex-col gap-2 ml-4">
                        <Badge 
                          variant="outline" 
                          className={getStatusColor(requirement.status)}
                        >
                          {getStatusIcon(requirement.status)}
                          <span className="ml-1 capitalize">{requirement.status}</span>
                        </Badge>
                        <Badge 
                          variant="outline" 
                          className={getPriorityColor(requirement.priority)}
                        >
                          {requirement.priority.replace('-', ' ')}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <div className="flex items-center gap-4">
                        <span>Type: {requirement.type}</span>
                        <span>Stakeholder: {requirement.stakeholder}</span>
                      </div>
                      {requirement.approved_at && (
                        <span>Approved: {new Date(requirement.approved_at).toLocaleDateString()}</span>
                      )}
                    </div>
                    {requirement.acceptance_criteria.length > 0 && (
                      <div className="mt-3">
                        <p className="text-sm font-medium mb-2">Acceptance Criteria:</p>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          {requirement.acceptance_criteria.map((criteria, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="text-xs mt-1">•</span>
                              <span>{criteria}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 border border-dashed rounded-lg">
              <div className="flex flex-col items-center gap-4">
                <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center">
                  <FileText className="h-8 w-8 text-muted-foreground" />
                </div>
                <div>
                  <p className="text-muted-foreground mb-2">No requirements defined yet</p>
                  <p className="text-sm text-muted-foreground">
                    Start by adding functional and non-functional requirements
                  </p>
                </div>
                {!isArchived && (
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add First Requirement
                  </Button>
                )}
              </div>
            </div>
          )}
        </TabsContent>

        {/* User Stories Content */}
        <TabsContent value="user-stories" className="space-y-4">
          {userStories.length > 0 ? (
            <div className="space-y-4">
              {userStories.map((story) => (
                <Card key={story.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-base">{story.title}</CardTitle>
                        <div className="mt-2 space-y-1 text-sm text-muted-foreground">
                          <p><strong>As a</strong> {story.as_a}</p>
                          <p><strong>I want</strong> {story.i_want}</p>
                          <p><strong>So that</strong> {story.so_that}</p>
                        </div>
                      </div>
                      <div className="flex flex-col gap-2 ml-4">
                        <Badge 
                          variant="outline" 
                          className={getStatusColor(story.status)}
                        >
                          {getStatusIcon(story.status)}
                          <span className="ml-1 capitalize">{story.status}</span>
                        </Badge>
                        {story.story_points && (
                          <Badge variant="outline">
                            {story.story_points} pts
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {story.acceptance_criteria.length > 0 && (
                      <div>
                        <p className="text-sm font-medium mb-2">Acceptance Criteria:</p>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          {story.acceptance_criteria.map((criteria, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="text-xs mt-1">•</span>
                              <span>{criteria}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 border border-dashed rounded-lg">
              <div className="flex flex-col items-center gap-4">
                <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center">
                  <Users className="h-8 w-8 text-muted-foreground" />
                </div>
                <div>
                  <p className="text-muted-foreground mb-2">No user stories created yet</p>
                  <p className="text-sm text-muted-foreground">
                    Create user stories to capture user needs and expectations
                  </p>
                </div>
                {!isArchived && (
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add First User Story
                  </Button>
                )}
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
