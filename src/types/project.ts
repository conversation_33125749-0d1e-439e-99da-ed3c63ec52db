export type ProjectType = 'internal' | 'external';
export type ProjectStatus = 'backlog' | 'not-started' | 'in-progress' | 'completed' | 'archived';
export type TaskStatus = 'to-do' | 'in-progress' | 'done';

// New priority system types
export type PriorityLevel = 'P0' | 'P1' | 'P2' | 'P3' | 'P4';
export type EffortEstimate = 'S' | 'M' | 'L' | 'XL';
export type ImpactType = 'Revenue' | 'Platform' | 'Bug Fix' | 'R&D';

export interface Project {
  id: string;
  name: string;
  company_name: string;
  type: ProjectType;
  customer_name?: string;
  project_lead: string; // Legacy field - will be deprecated
  project_lead_id?: string;
  customer_lead?: string; // Legacy field - will be deprecated
  customer_lead_id?: string;
  customer_contact?: string;
  description?: string;
  status: ProjectStatus;
  start_date?: string;
  end_date?: string;
  original_end_date?: string;
  prd_document_link?: string;
  poc_url?: string;
  priority_order?: number; // Legacy field - will be deprecated
  created_at: string;
  updated_at: string;
  completed_at?: string;
  archived_at?: string;
  status_changed_at?: string;
  // New priority system fields
  priority_level?: PriorityLevel;
  effort_estimate?: EffortEstimate;
  impact_type?: ImpactType;
  impact_type_id?: string; // Database reference to impact_types table
  priority_assigned_at?: string;
  last_reviewed_at?: string;
  auto_escalated?: boolean;
}

export interface Task {
  id: string;
  project_id: string;
  name: string;
  description?: string;
  assignee: string; // Legacy field - will be deprecated
  assignee_id?: string;
  due_date?: string;
  status: TaskStatus;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

export interface SubTask {
  id: string;
  task_id: string;
  name: string;
  description?: string;
  assignee: string; // Legacy field - will be deprecated
  assignee_id?: string;
  due_date?: string;
  status: TaskStatus;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

export interface ProjectIntegration {
  id: string;
  project_id: string;
  integration_type: 'google_drive' | 'slack_webhook';
  integration_url: string;
  integration_data?: any;
  created_at: string;
  updated_at: string;
}

export interface ProjectWithDetails extends Project {
  tasks?: TaskWithSubTasks[];
  integrations?: ProjectIntegration[];
  progress?: number;
}

export interface TaskWithSubTasks extends Task {
  sub_tasks?: SubTask[];
}

// Priority system interfaces
export interface PriorityHistory {
  id: string;
  project_id: string;
  old_priority?: PriorityLevel;
  new_priority: PriorityLevel;
  changed_by?: string;
  change_reason?: string;
  auto_escalated: boolean;
  created_at: string;
}

export interface PriorityRule {
  id: string;
  from_priority: PriorityLevel;
  to_priority: PriorityLevel;
  max_days: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// ============================================================================
// SDLC TAB DATA MODELS
// ============================================================================

// Requirements Tab
export interface Requirement {
  id: string;
  project_id: string;
  title: string;
  description: string;
  type: 'functional' | 'non-functional' | 'business' | 'technical';
  priority: 'must-have' | 'should-have' | 'could-have' | 'wont-have';
  status: 'draft' | 'review' | 'approved' | 'implemented' | 'tested';
  stakeholder: string;
  acceptance_criteria: string[];
  created_at: string;
  updated_at: string;
  approved_at?: string;
  approved_by?: string;
}

export interface UserStory {
  id: string;
  project_id: string;
  requirement_id?: string;
  title: string;
  description: string;
  as_a: string; // "As a [user type]"
  i_want: string; // "I want [functionality]"
  so_that: string; // "So that [benefit]"
  acceptance_criteria: string[];
  story_points?: number;
  priority: number;
  status: 'draft' | 'ready' | 'in-progress' | 'done';
  created_at: string;
  updated_at: string;
}

// Planning Tab
export interface ProjectMilestone {
  id: string;
  project_id: string;
  name: string;
  description?: string;
  due_date: string;
  status: 'planned' | 'in-progress' | 'completed' | 'delayed';
  completion_percentage: number;
  dependencies: string[]; // milestone IDs
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

export interface ResourceAllocation {
  id: string;
  project_id: string;
  team_member_id: string;
  role: string;
  allocation_percentage: number; // 0-100
  start_date: string;
  end_date?: string;
  hourly_rate?: number;
  created_at: string;
  updated_at: string;
}

export interface RiskAssessment {
  id: string;
  project_id: string;
  risk_title: string;
  description: string;
  category: 'technical' | 'business' | 'resource' | 'timeline' | 'external';
  probability: 'low' | 'medium' | 'high';
  impact: 'low' | 'medium' | 'high';
  risk_score: number; // calculated from probability * impact
  mitigation_strategy: string;
  contingency_plan?: string;
  status: 'identified' | 'monitoring' | 'mitigated' | 'occurred' | 'closed';
  owner: string;
  created_at: string;
  updated_at: string;
}

export interface ArchitectureDecision {
  id: string;
  project_id: string;
  title: string;
  context: string;
  decision: string;
  rationale: string;
  consequences: string[];
  alternatives_considered: string[];
  status: 'proposed' | 'accepted' | 'deprecated' | 'superseded';
  decision_date: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

// Implementation Tab
export interface CodeRepository {
  id: string;
  project_id: string;
  name: string;
  url: string;
  type: 'github' | 'gitlab' | 'bitbucket' | 'azure-devops' | 'other';
  branch: string;
  is_primary: boolean;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface TechnicalSpecification {
  id: string;
  project_id: string;
  title: string;
  content: string; // Markdown content
  type: 'api' | 'database' | 'architecture' | 'integration' | 'security' | 'other';
  version: string;
  status: 'draft' | 'review' | 'approved' | 'implemented';
  created_by: string;
  reviewed_by?: string;
  created_at: string;
  updated_at: string;
  approved_at?: string;
}

export interface DevelopmentEnvironment {
  id: string;
  project_id: string;
  name: string;
  type: 'development' | 'staging' | 'testing' | 'production';
  url?: string;
  description?: string;
  setup_instructions: string;
  environment_variables: Record<string, string>;
  status: 'active' | 'inactive' | 'maintenance';
  created_at: string;
  updated_at: string;
}

// Testing Tab
export interface TestPlan {
  id: string;
  project_id: string;
  name: string;
  description: string;
  type: 'unit' | 'integration' | 'system' | 'acceptance' | 'performance' | 'security';
  test_cases: TestCase[];
  status: 'draft' | 'active' | 'completed' | 'archived';
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface TestCase {
  id: string;
  test_plan_id: string;
  title: string;
  description: string;
  preconditions: string[];
  test_steps: TestStep[];
  expected_result: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'not-run' | 'passed' | 'failed' | 'blocked' | 'skipped';
  assigned_to?: string;
  executed_at?: string;
  execution_notes?: string;
  created_at: string;
  updated_at: string;
}

export interface TestStep {
  step_number: number;
  action: string;
  expected_result: string;
}

export interface BugReport {
  id: string;
  project_id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in-progress' | 'resolved' | 'closed' | 'duplicate' | 'wont-fix';
  environment: string;
  steps_to_reproduce: string[];
  expected_behavior: string;
  actual_behavior: string;
  reported_by: string;
  assigned_to?: string;
  resolution?: string;
  created_at: string;
  updated_at: string;
  resolved_at?: string;
}

// Documentation Tab
export interface ProjectDocument {
  id: string;
  project_id: string;
  title: string;
  content: string; // Markdown content
  type: 'technical' | 'user-manual' | 'api' | 'knowledge-base' | 'process' | 'other';
  category?: string;
  version: string;
  status: 'draft' | 'review' | 'published' | 'archived';
  is_public: boolean;
  tags: string[];
  created_by: string;
  reviewed_by?: string;
  created_at: string;
  updated_at: string;
  published_at?: string;
}

export interface ApiDocumentation {
  id: string;
  project_id: string;
  endpoint_url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  title: string;
  description: string;
  parameters: ApiParameter[];
  request_body?: string; // JSON schema
  response_examples: ApiResponse[];
  status_codes: ApiStatusCode[];
  created_at: string;
  updated_at: string;
}

export interface ApiParameter {
  name: string;
  type: string;
  required: boolean;
  description: string;
  example?: string;
}

export interface ApiResponse {
  status_code: number;
  description: string;
  example: string; // JSON example
}

export interface ApiStatusCode {
  code: number;
  description: string;
}

// Deployment Tab
export interface DeploymentChecklist {
  id: string;
  project_id: string;
  name: string;
  environment: 'staging' | 'production' | 'testing';
  checklist_items: ChecklistItem[];
  status: 'pending' | 'in-progress' | 'completed' | 'failed';
  created_by: string;
  assigned_to?: string;
  scheduled_date?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface ChecklistItem {
  id: string;
  title: string;
  description?: string;
  is_completed: boolean;
  completed_by?: string;
  completed_at?: string;
  notes?: string;
}

export interface EnvironmentConfiguration {
  id: string;
  project_id: string;
  environment_name: string;
  configuration_data: Record<string, any>;
  secrets: string[]; // List of secret keys (values stored securely)
  status: 'active' | 'inactive' | 'deprecated';
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface ReleaseNote {
  id: string;
  project_id: string;
  version: string;
  release_date: string;
  title: string;
  description: string;
  features: string[];
  bug_fixes: string[];
  breaking_changes: string[];
  migration_notes?: string;
  status: 'draft' | 'published';
  created_by: string;
  created_at: string;
  updated_at: string;
  published_at?: string;
}

// Monitoring Tab
export interface PerformanceMetric {
  id: string;
  project_id: string;
  metric_name: string;
  metric_type: 'response_time' | 'throughput' | 'error_rate' | 'uptime' | 'custom';
  current_value: number;
  target_value?: number;
  unit: string;
  measurement_date: string;
  environment: string;
  created_at: string;
}

export interface UserFeedback {
  id: string;
  project_id: string;
  feedback_type: 'bug' | 'feature-request' | 'improvement' | 'compliment' | 'complaint';
  title: string;
  description: string;
  user_email?: string;
  user_role?: string;
  priority: 'low' | 'medium' | 'high';
  status: 'new' | 'acknowledged' | 'in-progress' | 'resolved' | 'closed';
  assigned_to?: string;
  resolution?: string;
  created_at: string;
  updated_at: string;
  resolved_at?: string;
}

export interface MaintenanceTask {
  id: string;
  project_id: string;
  title: string;
  description: string;
  task_type: 'security-update' | 'dependency-update' | 'performance-optimization' | 'bug-fix' | 'monitoring' | 'backup';
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
  scheduled_date?: string;
  estimated_duration?: number; // in minutes
  assigned_to?: string;
  completion_notes?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

// Extended Project interface with SDLC data
export interface ProjectWithSDLCDetails extends ProjectWithDetails {
  requirements?: Requirement[];
  user_stories?: UserStory[];
  milestones?: ProjectMilestone[];
  resource_allocations?: ResourceAllocation[];
  risk_assessments?: RiskAssessment[];
  architecture_decisions?: ArchitectureDecision[];
  code_repositories?: CodeRepository[];
  technical_specifications?: TechnicalSpecification[];
  development_environments?: DevelopmentEnvironment[];
  test_plans?: TestPlan[];
  bug_reports?: BugReport[];
  project_documents?: ProjectDocument[];
  api_documentation?: ApiDocumentation[];
  deployment_checklists?: DeploymentChecklist[];
  environment_configurations?: EnvironmentConfiguration[];
  release_notes?: ReleaseNote[];
  performance_metrics?: PerformanceMetric[];
  user_feedback?: UserFeedback[];
  maintenance_tasks?: MaintenanceTask[];
}