import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Project, ProjectWithDetails } from "@/types/project";
import { useToast } from "@/hooks/use-toast";
import { useGradualMigration } from "@/utils/migrationHelper";
import {
  useProjects as useProjectsNew,
  useProjectsBasic as useProjectsBasicNew,
  useCreateProject as useCreateProjectNew,
  useUpdateProject as useUpdateProjectNew,
  useDeleteProject as useDeleteProjectNew,
} from "./useProjectsNew";

// Legacy Supabase implementation
function useProjectsLegacy() {
  return useQuery({
    queryKey: ['projects'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          tasks (
            id,
            project_id,
            name,
            description,
            assignee,
            due_date,
            status,
            created_at,
            updated_at
          )
        `)
        .order('priority_order', { ascending: true });

      if (error) throw error;
      return data as ProjectWithDetails[];
    },
  });
}

export function useProjects() {
  return useGradualMigration(
    'useProjects',
    useProjectsLegacy,
    useProjectsNew
  );
}

// Legacy Supabase implementation
function useProjectsBasicLegacy() {
  return useQuery({
    queryKey: ['projects-basic'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .order('priority_order', { ascending: true });

      if (error) throw error;
      return data as Project[];
    },
  });
}

export function useProjectsBasic() {
  return useGradualMigration(
    'useProjectsBasic',
    useProjectsBasicLegacy,
    useProjectsBasicNew
  );
}

export function useProjectProgress() {
  return useQuery({
    queryKey: ['project-progress'],
    queryFn: async () => {
      const { data: projects } = await supabase
        .from('projects')
        .select('id');

      if (!projects) return {};

      const progressMap: Record<string, number> = {};

      // Calculate progress manually for each project
      for (const project of projects) {
        const { data: tasks } = await supabase
          .from('tasks')
          .select('status')
          .eq('project_id', project.id);

        const totalTasks = tasks?.length || 0;
        const completedTasks = tasks?.filter(task => task.status === 'done').length || 0;
        const progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

        progressMap[project.id] = progress;
      }

      return progressMap;
    },
  });
}

export function useReorderProjects() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ projectIds, newOrders }: { projectIds: string[], newOrders: number[] }) => {
      const { error } = await supabase.rpc('reorder_projects', {
        project_ids: projectIds,
        new_orders: newOrders
      });
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects-basic'] });
    },
    onError: (error) => {
      console.error('Error reordering projects:', error);
      toast({
        title: "Error",
        description: "Failed to reorder projects. Please try again.",
        variant: "destructive",
      });
    },
  });
}