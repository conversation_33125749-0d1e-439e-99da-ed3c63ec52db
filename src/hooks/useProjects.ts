import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Project, ProjectWithDetails } from "@/types/project";
import { useToast } from "@/hooks/use-toast";
import { api } from "@/services/apiClient";

export function useProjects() {
  return useQuery({
    queryKey: ['projects'],
    queryFn: async (): Promise<ProjectWithDetails[]> => {
      try {
        const response = await api.projects.list();
        const projects = response.data || [];

        // Transform backend data to match frontend interface
        return projects.map(project => ({
          ...project,
          // Map backend fields to frontend fields
          company_name: project.customerName || '',
          customer_name: project.customerName,
          project_lead: project.projectLead,
          customer_lead: project.customerLead,
          customer_contact: project.customerContact,
          priority_order: 0, // Default value
          start_date: undefined,
          end_date: undefined,
          original_end_date: undefined,
          prd_document_link: undefined,
          poc_url: undefined,
          completed_at: undefined,
          archived_at: undefined,
          status_changed_at: undefined,
          priority_level: project.priorityLevel,
          effort_estimate: project.effortEstimate,
          impact_type: undefined,
          impact_type_id: undefined,
          priority_assigned_at: undefined,
          last_reviewed_at: undefined,
          auto_escalated: undefined,
          // Transform tasks
          tasks: project.tasks?.map(task => ({
            ...task,
            project_id: task.projectId,
            assignee: task.assigneeId || '',
            assignee_id: task.assigneeId,
            due_date: task.dueDate,
            completed_at: undefined,
          })) || [],
          integrations: [],
          progress: 0,
        }));
      } catch (error) {
        console.error('Error fetching projects:', error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Legacy Supabase implementation
function useProjectsBasicLegacy() {
  return useQuery({
    queryKey: ['projects-basic'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .order('priority_order', { ascending: true });

      if (error) throw error;
      return data as Project[];
    },
  });
}

export function useProjectsBasic() {
  return useGradualMigration(
    'useProjectsBasic',
    useProjectsBasicLegacy,
    useProjectsBasicNew
  );
}

export function useProjectProgress() {
  return useQuery({
    queryKey: ['project-progress'],
    queryFn: async () => {
      const { data: projects } = await supabase
        .from('projects')
        .select('id');

      if (!projects) return {};

      const progressMap: Record<string, number> = {};

      // Calculate progress manually for each project
      for (const project of projects) {
        const { data: tasks } = await supabase
          .from('tasks')
          .select('status')
          .eq('project_id', project.id);

        const totalTasks = tasks?.length || 0;
        const completedTasks = tasks?.filter(task => task.status === 'done').length || 0;
        const progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

        progressMap[project.id] = progress;
      }

      return progressMap;
    },
  });
}

export function useReorderProjects() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ projectIds, newOrders }: { projectIds: string[], newOrders: number[] }) => {
      const { error } = await supabase.rpc('reorder_projects', {
        project_ids: projectIds,
        new_orders: newOrders
      });
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects-basic'] });
    },
    onError: (error) => {
      console.error('Error reordering projects:', error);
      toast({
        title: "Error",
        description: "Failed to reorder projects. Please try again.",
        variant: "destructive",
      });
    },
  });
}