/**
 * Projects Hooks (New API Client)
 * Replaces Supabase-based project hooks with our backend API
 */

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api, ApiError } from "@/services/apiClient";
import { useToast } from "@/hooks/use-toast";

// Types (matching our backend schema)
export interface Project {
  id: string;
  name: string;
  type: 'internal' | 'external';
  customerName?: string;
  projectLead: string;
  customerLead?: string;
  customerContact?: string;
  description?: string;
  status: 'active' | 'completed' | 'on_hold' | 'cancelled';
  priorityLevel: 'P0' | 'P1' | 'P2' | 'P3' | 'P4';
  effortEstimate: 'XS' | 'S' | 'M' | 'L' | 'XL';
  createdBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Task {
  id: string;
  projectId: string;
  name: string;
  description?: string;
  assigneeId?: string;
  dueDate?: string;
  status: 'to-do' | 'in-progress' | 'done' | 'cancelled';
  priorityLevel: 'P0' | 'P1' | 'P2' | 'P3' | 'P4';
  effortEstimate: 'XS' | 'S' | 'M' | 'L' | 'XL';
  createdBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ProjectWithTasks extends Project {
  tasks: Task[];
}

// Hooks
export function useProjects() {
  return useQuery({
    queryKey: ['projects'],
    queryFn: async (): Promise<ProjectWithTasks[]> => {
      try {
        const response = await api.projects.list();
        return response.data || [];
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useProjectsBasic() {
  return useQuery({
    queryKey: ['projects-basic'],
    queryFn: async (): Promise<Project[]> => {
      try {
        const response = await api.projects.list();
        return response.data || [];
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useProject(id: string) {
  return useQuery({
    queryKey: ['project', id],
    queryFn: async (): Promise<Project> => {
      try {
        const response = await api.projects.get(id);
        return response.data;
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useProjectDetail(id: string) {
  return useQuery({
    queryKey: ['project-detail', id],
    queryFn: async (): Promise<ProjectWithTasks> => {
      try {
        // Fetch project and tasks in parallel
        const [projectResponse, tasksResponse] = await Promise.all([
          api.projects.get(id),
          api.tasks.list({ projectId: id }),
        ]);

        return {
          ...projectResponse.data,
          tasks: tasksResponse.data || [],
        };
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes (more frequent updates for detail view)
  });
}

export function useCreateProject() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (projectData: Partial<Project>) => {
      try {
        const response = await api.projects.create(projectData);
        return response.data;
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      // Invalidate and refetch projects
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects-basic'] });
      
      toast({
        title: "Success",
        description: "Project created successfully.",
      });
    },
    onError: (error: Error) => {
      console.error('Error creating project:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create project. Please try again.",
        variant: "destructive",
      });
    },
  });
}

export function useUpdateProject() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<Project> }) => {
      try {
        const response = await api.projects.update(id, updates);
        return response.data;
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects-basic'] });
      queryClient.invalidateQueries({ queryKey: ['project', data.id] });
      queryClient.invalidateQueries({ queryKey: ['project-detail', data.id] });
      
      toast({
        title: "Success",
        description: "Project updated successfully.",
      });
    },
    onError: (error: Error) => {
      console.error('Error updating project:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update project. Please try again.",
        variant: "destructive",
      });
    },
  });
}

export function useDeleteProject() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        const response = await api.projects.delete(id);
        return response.data;
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    onSuccess: () => {
      // Invalidate projects queries
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects-basic'] });
      
      toast({
        title: "Success",
        description: "Project deleted successfully.",
      });
    },
    onError: (error: Error) => {
      console.error('Error deleting project:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete project. Please try again.",
        variant: "destructive",
      });
    },
  });
}

export function useUpdateProjectStatus() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ projectId, status }: { projectId: string; status: string }) => {
      try {
        const response = await api.projects.update(projectId, { status });
        return response.data;
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      // Optimistic update with immediate cache refresh
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects-basic'] });
      queryClient.invalidateQueries({ queryKey: ['project', data.id] });
      queryClient.invalidateQueries({ queryKey: ['project-detail', data.id] });
      
      toast({
        title: "Success",
        description: "Project status updated successfully.",
      });
    },
    onError: (error: Error) => {
      console.error('Error updating project status:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update project status. Please try again.",
        variant: "destructive",
      });
    },
  });
}
