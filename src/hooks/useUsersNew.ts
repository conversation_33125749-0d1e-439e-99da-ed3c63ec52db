/**
 * Users/Team Hooks (New API Client)
 * Replaces Supabase-based user hooks with our backend API
 */

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api, ApiError } from "@/services/apiClient";
import { useToast } from "@/hooks/use-toast";

// User interfaces
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'guest' | 'member' | 'admin' | 'super_admin';
  avatarUrl?: string;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface TeamMember extends User {
  // Additional team-specific fields can be added here
}

export interface CreateTeamMemberData {
  email: string;
  name: string;
  role?: 'guest' | 'member' | 'admin' | 'super_admin';
}

export interface UpdateTeamMemberData {
  name?: string;
  role?: 'guest' | 'member' | 'admin' | 'super_admin';
  isActive?: boolean;
}

// Hooks
export function useUsers() {
  return useQuery({
    queryKey: ['users'],
    queryFn: async (): Promise<User[]> => {
      try {
        const response = await api.users.list();
        return response.data || [];
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useTeamMembers(activeOnly = false) {
  return useQuery({
    queryKey: ['team-members', activeOnly],
    queryFn: async (): Promise<TeamMember[]> => {
      try {
        const response = await api.users.list();
        let users = response.data || [];
        
        if (activeOnly) {
          users = users.filter(user => user.isActive);
        }
        
        return users;
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useUser(id: string) {
  return useQuery({
    queryKey: ['user', id],
    queryFn: async (): Promise<User> => {
      try {
        const response = await api.users.get(id);
        return response.data;
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useUpdateUser() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: UpdateTeamMemberData }) => {
      try {
        const response = await api.users.update(id, updates);
        return response.data;
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['team-members'] });
      queryClient.invalidateQueries({ queryKey: ['user', data.id] });
      
      toast({
        title: "Success",
        description: "User updated successfully.",
      });
    },
    onError: (error: Error) => {
      console.error('Error updating user:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update user. Please try again.",
        variant: "destructive",
      });
    },
  });
}

export function useDeleteUser() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        const response = await api.users.delete(id);
        return response.data;
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['team-members'] });
      
      toast({
        title: "Success",
        description: "User deleted successfully.",
      });
    },
    onError: (error: Error) => {
      console.error('Error deleting user:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete user. Please try again.",
        variant: "destructive",
      });
    },
  });
}

// RBAC-specific hooks
export function useUserRoles(userId: string) {
  return useQuery({
    queryKey: ['user-roles', userId],
    queryFn: async () => {
      try {
        const response = await api.rbac.userPermissions(userId);
        return response.data;
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useAssignRole() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ userId, roleName }: { userId: string; roleName: string }) => {
      try {
        const response = await api.rbac.assignRole(userId, roleName);
        return response.data;
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    onSuccess: (_, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['user-roles', variables.userId] });
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['team-members'] });
      
      toast({
        title: "Success",
        description: "Role assigned successfully.",
      });
    },
    onError: (error: Error) => {
      console.error('Error assigning role:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to assign role. Please try again.",
        variant: "destructive",
      });
    },
  });
}

export function useRemoveRole() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ userId, roleName }: { userId: string; roleName: string }) => {
      try {
        const response = await api.rbac.removeRole(userId, roleName);
        return response.data;
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    onSuccess: (_, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['user-roles', variables.userId] });
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['team-members'] });
      
      toast({
        title: "Success",
        description: "Role removed successfully.",
      });
    },
    onError: (error: Error) => {
      console.error('Error removing role:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to remove role. Please try again.",
        variant: "destructive",
      });
    },
  });
}

// Permission checking hooks
export function useHasPermission(permission: string) {
  return useQuery({
    queryKey: ['has-permission', permission],
    queryFn: async () => {
      try {
        const response = await api.rbac.checkPermission(permission);
        return response.data?.hasPermission || false;
      } catch (error) {
        console.error('Permission check failed:', error);
        return false;
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useHasRole(role: string) {
  return useQuery({
    queryKey: ['has-role', role],
    queryFn: async () => {
      try {
        const response = await api.rbac.checkRole(role);
        return response.data?.hasRole || false;
      } catch (error) {
        console.error('Role check failed:', error);
        return false;
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}
