/**
 * Migration Helper
 * Utilities to help migrate from Supabase to new API client
 */

// Type mappings from Supabase to new API
export const statusMapping = {
  // Project status mapping
  project: {
    'active': 'active',
    'completed': 'completed',
    'on-hold': 'on_hold',
    'cancelled': 'cancelled',
  },
  // Task status mapping
  task: {
    'to-do': 'to-do',
    'in-progress': 'in-progress',
    'done': 'done',
    'cancelled': 'cancelled',
  },
  // Priority mapping
  priority: {
    'P0': 'P0',
    'P1': 'P1',
    'P2': 'P2',
    'P3': 'P3',
    'P4': 'P4',
  },
  // Effort mapping
  effort: {
    'XS': 'XS',
    'S': 'S',
    'M': 'M',
    'L': 'L',
    'XL': 'XL',
  },
};

// Field name mappings
export const fieldMapping = {
  // Supabase snake_case to camelCase
  'created_at': 'createdAt',
  'updated_at': 'updatedAt',
  'project_id': 'projectId',
  'assignee_id': 'assigneeId',
  'due_date': 'dueDate',
  'customer_name': 'customerName',
  'project_lead': 'projectLead',
  'customer_lead': 'customerLead',
  'customer_contact': 'customerContact',
  'priority_level': 'priorityLevel',
  'effort_estimate': 'effortEstimate',
  'is_active': 'isActive',
  'avatar_url': 'avatarUrl',
  'last_login_at': 'lastLoginAt',
  'created_by': 'createdBy',
};

/**
 * Transform Supabase data to new API format
 */
export function transformFromSupabase(data: any): any {
  if (!data) return data;
  
  if (Array.isArray(data)) {
    return data.map(transformFromSupabase);
  }
  
  if (typeof data === 'object') {
    const transformed: any = {};
    
    for (const [key, value] of Object.entries(data)) {
      // Map field names
      const newKey = fieldMapping[key as keyof typeof fieldMapping] || key;
      transformed[newKey] = transformFromSupabase(value);
    }
    
    return transformed;
  }
  
  return data;
}

/**
 * Transform new API data to Supabase format (for backward compatibility)
 */
export function transformToSupabase(data: any): any {
  if (!data) return data;
  
  if (Array.isArray(data)) {
    return data.map(transformToSupabase);
  }
  
  if (typeof data === 'object') {
    const transformed: any = {};
    
    // Reverse mapping
    const reverseFieldMapping = Object.fromEntries(
      Object.entries(fieldMapping).map(([k, v]) => [v, k])
    );
    
    for (const [key, value] of Object.entries(data)) {
      // Map field names back
      const newKey = reverseFieldMapping[key] || key;
      transformed[newKey] = transformToSupabase(value);
    }
    
    return transformed;
  }
  
  return data;
}

/**
 * Migration status tracker
 */
export class MigrationTracker {
  private static instance: MigrationTracker;
  private migratedComponents: Set<string> = new Set();
  
  static getInstance(): MigrationTracker {
    if (!MigrationTracker.instance) {
      MigrationTracker.instance = new MigrationTracker();
    }
    return MigrationTracker.instance;
  }
  
  markAsMigrated(componentName: string): void {
    this.migratedComponents.add(componentName);
    console.log(`✅ Migrated: ${componentName}`);
  }
  
  isMigrated(componentName: string): boolean {
    return this.migratedComponents.has(componentName);
  }
  
  getMigrationStatus(): { migrated: string[]; total: number } {
    return {
      migrated: Array.from(this.migratedComponents),
      total: this.migratedComponents.size,
    };
  }
  
  logStatus(): void {
    const status = this.getMigrationStatus();
    console.log(`🔄 Migration Status: ${status.total} components migrated`);
    console.log('Migrated components:', status.migrated);
  }
}

/**
 * Feature flag for gradual migration
 */
export const useNewAPI = () => {
  // Check environment variable or localStorage flag
  const envFlag = import.meta.env.VITE_USE_NEW_API;
  const localFlag = localStorage.getItem('useNewAPI');
  
  // Default to true for development, false for production
  const defaultValue = import.meta.env.DEV;
  
  if (envFlag !== undefined) {
    return envFlag === 'true';
  }
  
  if (localFlag !== null) {
    return localFlag === 'true';
  }
  
  return defaultValue;
};

/**
 * Hook wrapper for gradual migration
 */
export function useGradualMigration<T>(
  componentName: string,
  oldHook: () => T,
  newHook: () => T
): T {
  const shouldUseNewAPI = useNewAPI();
  const tracker = MigrationTracker.getInstance();
  
  if (shouldUseNewAPI) {
    if (!tracker.isMigrated(componentName)) {
      tracker.markAsMigrated(componentName);
    }
    return newHook();
  }
  
  return oldHook();
}

/**
 * Error boundary for API migration
 */
export function withMigrationFallback<T>(
  newAPICall: () => Promise<T>,
  fallbackCall: () => Promise<T>,
  componentName: string
): Promise<T> {
  return newAPICall().catch((error) => {
    console.warn(`⚠️ New API failed for ${componentName}, falling back to Supabase:`, error);
    return fallbackCall();
  });
}

export default {
  transformFromSupabase,
  transformToSupabase,
  MigrationTracker,
  useNewAPI,
  useGradualMigration,
  withMigrationFallback,
};
