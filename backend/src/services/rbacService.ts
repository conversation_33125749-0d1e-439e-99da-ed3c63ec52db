/**
 * RBAC Service
 * Handles role-based access control operations
 */

import { prisma } from '@/config/database';
import { cache } from '@/config/redis';
import { logger } from '@/utils/logger';
import { createError } from '@/middleware/errorHandler';
import { UserRole } from '@prisma/client';

interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
}

interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: Permission[];
}

interface UserPermissions {
  userId: string;
  roles: Role[];
  permissions: string[];
}

export class RBACService {
  private readonly CACHE_TTL = 15 * 60; // 15 minutes
  private readonly CACHE_PREFIX = 'rbac:';

  /**
   * Get user permissions with caching
   */
  async getUserPermissions(userId: string): Promise<UserPermissions> {
    const cacheKey = `${this.CACHE_PREFIX}user:${userId}`;
    
    // Try to get from cache first
    const cached = await cache.get<UserPermissions>(cacheKey);
    if (cached) {
      return cached;
    }

    // Fetch from database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRoles: {
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!user) {
      throw createError('User not found', 404);
    }

    // Build roles and permissions
    const roles: Role[] = user.userRoles.map(userRole => ({
      id: userRole.role.id,
      name: userRole.role.name,
      description: userRole.role.description || undefined,
      permissions: userRole.role.permissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        resource: rp.permission.resource,
        action: rp.permission.action,
        description: rp.permission.description || undefined,
      })),
    }));

    // Collect all unique permissions
    const permissionSet = new Set<string>();
    roles.forEach(role => {
      role.permissions.forEach(permission => {
        permissionSet.add(permission.name);
      });
    });

    const userPermissions: UserPermissions = {
      userId,
      roles,
      permissions: Array.from(permissionSet),
    };

    // Cache the result
    await cache.set(cacheKey, userPermissions, this.CACHE_TTL);

    return userPermissions;
  }

  /**
   * Check if user has specific permission
   */
  async hasPermission(userId: string, permissionName: string): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      return userPermissions.permissions.includes(permissionName);
    } catch (error) {
      logger.error(`Permission check failed for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Check if user has any of the specified permissions
   */
  async hasAnyPermission(userId: string, permissionNames: string[]): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      return permissionNames.some(permission => 
        userPermissions.permissions.includes(permission)
      );
    } catch (error) {
      logger.error(`Permission check failed for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Check if user has all specified permissions
   */
  async hasAllPermissions(userId: string, permissionNames: string[]): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      return permissionNames.every(permission => 
        userPermissions.permissions.includes(permission)
      );
    } catch (error) {
      logger.error(`Permission check failed for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Check if user has specific role
   */
  async hasRole(userId: string, roleName: string): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      return userPermissions.roles.some(role => role.name === roleName);
    } catch (error) {
      logger.error(`Role check failed for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Check if user has any of the specified roles
   */
  async hasAnyRole(userId: string, roleNames: string[]): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      return roleNames.some(roleName => 
        userPermissions.roles.some(role => role.name === roleName)
      );
    } catch (error) {
      logger.error(`Role check failed for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Assign role to user
   */
  async assignRole(userId: string, roleName: string, assignedBy?: string): Promise<void> {
    try {
      // Find the role
      const role = await prisma.role.findUnique({
        where: { name: roleName },
      });

      if (!role) {
        throw createError(`Role '${roleName}' not found`, 404);
      }

      // Check if user already has this role
      const existingUserRole = await prisma.userRole_Relation.findUnique({
        where: {
          userId_roleId: {
            userId,
            roleId: role.id,
          },
        },
      });

      if (existingUserRole) {
        throw createError(`User already has role '${roleName}'`, 409);
      }

      // Assign the role
      await prisma.userRole_Relation.create({
        data: {
          userId,
          roleId: role.id,
          assignedBy,
        },
      });

      // Clear user's permission cache
      await this.clearUserCache(userId);

      logger.info(`Role '${roleName}' assigned to user ${userId}`, {
        userId,
        roleName,
        assignedBy,
      });
    } catch (error) {
      logger.error(`Failed to assign role '${roleName}' to user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Remove role from user
   */
  async removeRole(userId: string, roleName: string): Promise<void> {
    try {
      // Find the role
      const role = await prisma.role.findUnique({
        where: { name: roleName },
      });

      if (!role) {
        throw createError(`Role '${roleName}' not found`, 404);
      }

      // Remove the role assignment
      const deleted = await prisma.userRole_Relation.deleteMany({
        where: {
          userId,
          roleId: role.id,
        },
      });

      if (deleted.count === 0) {
        throw createError(`User does not have role '${roleName}'`, 404);
      }

      // Clear user's permission cache
      await this.clearUserCache(userId);

      logger.info(`Role '${roleName}' removed from user ${userId}`, {
        userId,
        roleName,
      });
    } catch (error) {
      logger.error(`Failed to remove role '${roleName}' from user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get all available roles
   */
  async getAllRoles(): Promise<Role[]> {
    const cacheKey = `${this.CACHE_PREFIX}roles:all`;
    
    // Try cache first
    const cached = await cache.get<Role[]>(cacheKey);
    if (cached) {
      return cached;
    }

    // Fetch from database
    const roles = await prisma.role.findMany({
      include: {
        permissions: {
          include: {
            permission: true,
          },
        },
      },
      orderBy: { name: 'asc' },
    });

    const result: Role[] = roles.map(role => ({
      id: role.id,
      name: role.name,
      description: role.description || undefined,
      permissions: role.permissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        resource: rp.permission.resource,
        action: rp.permission.action,
        description: rp.permission.description || undefined,
      })),
    }));

    // Cache the result
    await cache.set(cacheKey, result, this.CACHE_TTL);

    return result;
  }

  /**
   * Get all available permissions
   */
  async getAllPermissions(): Promise<Permission[]> {
    const cacheKey = `${this.CACHE_PREFIX}permissions:all`;
    
    // Try cache first
    const cached = await cache.get<Permission[]>(cacheKey);
    if (cached) {
      return cached;
    }

    // Fetch from database
    const permissions = await prisma.permission.findMany({
      orderBy: [{ resource: 'asc' }, { action: 'asc' }],
    });

    const result: Permission[] = permissions.map(permission => ({
      id: permission.id,
      name: permission.name,
      resource: permission.resource,
      action: permission.action,
      description: permission.description || undefined,
    }));

    // Cache the result
    await cache.set(cacheKey, result, this.CACHE_TTL);

    return result;
  }

  /**
   * Clear user's permission cache
   */
  async clearUserCache(userId: string): Promise<void> {
    const cacheKey = `${this.CACHE_PREFIX}user:${userId}`;
    await cache.del(cacheKey);
  }

  /**
   * Clear all RBAC caches
   */
  async clearAllCaches(): Promise<void> {
    const keys = await cache.keys(`${this.CACHE_PREFIX}*`);
    if (keys.length > 0) {
      await Promise.all(keys.map(key => cache.del(key)));
    }
  }

  /**
   * Initialize default guest role for new users
   */
  async initializeGuestRole(userId: string): Promise<void> {
    try {
      // Check if user already has any roles
      const existingRoles = await prisma.userRole_Relation.findMany({
        where: { userId },
      });

      if (existingRoles.length === 0) {
        // Assign guest role by default
        await this.assignRole(userId, 'guest');
        logger.info(`Guest role assigned to new user ${userId}`);
      }
    } catch (error) {
      logger.error(`Failed to initialize guest role for user ${userId}:`, error);
      // Don't throw error here as it's not critical
    }
  }
}

// Export singleton instance
export const rbacService = new RBACService();
