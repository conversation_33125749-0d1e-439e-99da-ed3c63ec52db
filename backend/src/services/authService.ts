/**
 * Authentication Service
 * Handles user authentication, JWT tokens, and Google OAuth
 */

import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { prisma } from '@/config/database';
import { cache } from '@/config/redis';
import { logger, logAuth } from '@/utils/logger';
import { createError } from '@/middleware/errorHandler';
import { UserRole } from '@prisma/client';

interface TokenPayload {
  id: string;
  email: string;
  name: string;
  role: string;
}

interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

interface GoogleUserInfo {
  id: string;
  email: string;
  name: string;
  picture?: string;
  verified_email: boolean;
}

export class AuthService {
  private readonly JWT_SECRET: string;
  private readonly JWT_REFRESH_SECRET: string;
  private readonly JWT_EXPIRES_IN: string;
  private readonly JWT_REFRESH_EXPIRES_IN: string;

  constructor() {
    this.JWT_SECRET = process.env.JWT_SECRET!;
    this.JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || this.JWT_SECRET + '_refresh';
    this.JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m';
    this.JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

    if (!this.JWT_SECRET) {
      throw new Error('JWT_SECRET environment variable is required');
    }
  }

  /**
   * Generate JWT token pair (access + refresh)
   */
  generateTokens(payload: TokenPayload): TokenPair {
    const accessToken = jwt.sign(payload, this.JWT_SECRET, {
      expiresIn: this.JWT_EXPIRES_IN,
    });

    const refreshToken = jwt.sign(payload, this.JWT_REFRESH_SECRET, {
      expiresIn: this.JWT_REFRESH_EXPIRES_IN,
    });

    return { accessToken, refreshToken };
  }

  /**
   * Verify access token
   */
  verifyAccessToken(token: string): TokenPayload {
    try {
      return jwt.verify(token, this.JWT_SECRET) as TokenPayload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw createError('Access token expired', 401);
      }
      throw createError('Invalid access token', 401);
    }
  }

  /**
   * Verify refresh token
   */
  verifyRefreshToken(token: string): TokenPayload {
    try {
      return jwt.verify(token, this.JWT_REFRESH_SECRET) as TokenPayload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw createError('Refresh token expired', 401);
      }
      throw createError('Invalid refresh token', 401);
    }
  }

  /**
   * Hash password
   */
  async hashPassword(password: string): Promise<string> {
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
    return bcrypt.hash(password, saltRounds);
  }

  /**
   * Compare password
   */
  async comparePassword(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  /**
   * Create or update user from Google OAuth
   */
  async handleGoogleAuth(googleUser: GoogleUserInfo) {
    try {
      // Check if user exists by Google ID
      let user = await prisma.user.findUnique({
        where: { googleId: googleUser.id },
      });

      if (!user) {
        // Check if user exists by email
        user = await prisma.user.findUnique({
          where: { email: googleUser.email },
        });

        if (user) {
          // Link Google account to existing user
          user = await prisma.user.update({
            where: { id: user.id },
            data: {
              googleId: googleUser.id,
              avatarUrl: googleUser.picture,
              lastLoginAt: new Date(),
            },
          });
        } else {
          // Create new user
          user = await prisma.user.create({
            data: {
              email: googleUser.email,
              googleId: googleUser.id,
              name: googleUser.name,
              avatarUrl: googleUser.picture,
              role: UserRole.guest, // Default role is guest
              isActive: true,
              lastLoginAt: new Date(),
            },
          });

          logAuth('User registered via Google OAuth', user.id, user.email);
        }
      } else {
        // Update existing user's last login
        user = await prisma.user.update({
          where: { id: user.id },
          data: {
            name: googleUser.name, // Update name in case it changed
            avatarUrl: googleUser.picture,
            lastLoginAt: new Date(),
          },
        });
      }

      logAuth('User authenticated via Google OAuth', user.id, user.email);

      // Generate tokens
      const tokenPayload: TokenPayload = {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
      };

      const tokens = this.generateTokens(tokenPayload);

      // Store refresh token in Redis with expiration
      const refreshTokenKey = `refresh_token:${user.id}`;
      await cache.set(refreshTokenKey, tokens.refreshToken, 7 * 24 * 60 * 60); // 7 days

      return {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          avatarUrl: user.avatarUrl,
        },
        tokens,
      };
    } catch (error) {
      logger.error('Google OAuth authentication failed:', error);
      throw createError('Authentication failed', 500);
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshAccessToken(refreshToken: string) {
    try {
      // Verify refresh token
      const payload = this.verifyRefreshToken(refreshToken);

      // Check if refresh token exists in Redis
      const refreshTokenKey = `refresh_token:${payload.id}`;
      const storedToken = await cache.get<string>(refreshTokenKey);

      if (!storedToken || storedToken !== refreshToken) {
        throw createError('Invalid refresh token', 401);
      }

      // Get current user data
      const user = await prisma.user.findUnique({
        where: { id: payload.id },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          isActive: true,
        },
      });

      if (!user || !user.isActive) {
        throw createError('User not found or inactive', 401);
      }

      // Generate new tokens
      const newTokenPayload: TokenPayload = {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
      };

      const tokens = this.generateTokens(newTokenPayload);

      // Update refresh token in Redis
      await cache.set(refreshTokenKey, tokens.refreshToken, 7 * 24 * 60 * 60);

      logAuth('Access token refreshed', user.id, user.email);

      return {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
        },
        tokens,
      };
    } catch (error) {
      logger.error('Token refresh failed:', error);
      throw error;
    }
  }

  /**
   * Logout user (invalidate refresh token)
   */
  async logout(userId: string): Promise<void> {
    try {
      const refreshTokenKey = `refresh_token:${userId}`;
      await cache.del(refreshTokenKey);

      logAuth('User logged out', userId);
    } catch (error) {
      logger.error('Logout failed:', error);
      throw createError('Logout failed', 500);
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        avatarUrl: true,
        isActive: true,
        lastLoginAt: true,
        createdAt: true,
      },
    });

    if (!user) {
      throw createError('User not found', 404);
    }

    return user;
  }

  /**
   * Update user's last login time
   */
  async updateLastLogin(userId: string): Promise<void> {
    await prisma.user.update({
      where: { id: userId },
      data: { lastLoginAt: new Date() },
    });
  }
}

// Export singleton instance
export const authService = new AuthService();
