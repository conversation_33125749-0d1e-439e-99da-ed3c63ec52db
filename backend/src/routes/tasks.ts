/**
 * Task Routes
 * Handles task management operations
 */

import { Router } from 'express';
import { async<PERSON>and<PERSON>, createError } from '@/middleware/errorHandler';
import {
  requireTaskRead,
  requireTaskCreate,
  requireTaskUpdate,
  requireTaskDelete,
} from '@/middleware/rbac';
import { prisma } from '@/config/database';
import { logger } from '@/utils/logger';

const router = Router();

/**
 * @route   GET /api/v1/tasks
 * @desc    Get all tasks
 * @access  Private (requires tasks.read permission)
 */
router.get('/', requireTaskRead, asyncHandler(async (req, res) => {
  const { projectId, status, assigneeId, limit, offset } = req.query;

  const where: any = {};
  if (projectId) {
    where.projectId = projectId as string;
  }
  if (status) {
    where.status = status as string;
  }
  if (assigneeId) {
    where.assigneeId = assigneeId as string;
  }

  const tasks = await prisma.task.findMany({
    where,
    include: {
      project: {
        select: {
          id: true,
          name: true,
        },
      },
      assignee: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      creator: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
    orderBy: [
      { priorityLevel: 'asc' },
      { createdAt: 'desc' },
    ],
    take: limit ? parseInt(limit as string) : undefined,
    skip: offset ? parseInt(offset as string) : undefined,
  });

  res.status(200).json({
    status: 'success',
    data: tasks,
  });
}));

/**
 * @route   GET /api/v1/tasks/:id
 * @desc    Get task by ID
 * @access  Private (requires tasks.read permission)
 */
router.get('/:id', requireTaskRead, asyncHandler(async (req, res) => {
  const { id } = req.params;

  const task = await prisma.task.findUnique({
    where: { id },
    include: {
      project: {
        select: {
          id: true,
          name: true,
        },
      },
      assignee: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      creator: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  if (!task) {
    throw createError('Task not found', 404);
  }

  res.status(200).json({
    status: 'success',
    data: task,
  });
}));

/**
 * @route   POST /api/v1/tasks
 * @desc    Create new task
 * @access  Private (requires tasks.create permission)
 */
router.post('/', requireTaskCreate, asyncHandler(async (req, res) => {
  const {
    projectId,
    name,
    description,
    assigneeId,
    dueDate,
    status,
    priorityLevel,
    effortEstimate,
  } = req.body;

  if (!projectId || !name) {
    throw createError('Project ID and name are required', 400);
  }

  // Verify project exists
  const project = await prisma.project.findUnique({
    where: { id: projectId },
    select: { id: true },
  });

  if (!project) {
    throw createError('Project not found', 404);
  }

  const task = await prisma.task.create({
    data: {
      projectId,
      name,
      description,
      assigneeId,
      dueDate: dueDate ? new Date(dueDate) : null,
      status: status || 'to-do',
      priorityLevel: priorityLevel || 'P3',
      effortEstimate: effortEstimate || 'M',
      createdBy: req.user!.id,
    },
    include: {
      project: {
        select: {
          id: true,
          name: true,
        },
      },
      assignee: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      creator: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  logger.info(`Task created: ${task.name}`, {
    taskId: task.id,
    projectId: task.projectId,
    createdBy: req.user!.id,
  });

  res.status(201).json({
    status: 'success',
    data: task,
  });
}));

/**
 * @route   PUT /api/v1/tasks/:id
 * @desc    Update task
 * @access  Private (requires tasks.update permission)
 */
router.put('/:id', requireTaskUpdate, asyncHandler(async (req, res) => {
  // TODO: Implement update task
  res.status(501).json({
    status: 'error',
    message: 'Update task endpoint not implemented yet',
  });
}));

/**
 * @route   DELETE /api/v1/tasks/:id
 * @desc    Delete task
 * @access  Private (requires tasks.delete permission)
 */
router.delete('/:id', requireTaskDelete, asyncHandler(async (req, res) => {
  // TODO: Implement delete task
  res.status(501).json({
    status: 'error',
    message: 'Delete task endpoint not implemented yet',
  });
}));

export default router;
