/**
 * Global Error Handler Middleware
 * Handles all errors in the application
 */

import { Request, Response, NextFunction } from 'express';
import { Prisma } from '@prisma/client';
import { logger } from '@/utils/logger';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

/**
 * Create an operational error
 */
export const createError = (message: string, statusCode: number = 500): AppError => {
  const error: AppError = new Error(message);
  error.statusCode = statusCode;
  error.isOperational = true;
  return error;
};

/**
 * Handle Prisma errors
 */
const handlePrismaError = (error: Prisma.PrismaClientKnownRequestError): AppError => {
  switch (error.code) {
    case 'P2002':
      // Unique constraint violation
      const field = error.meta?.target as string[] | undefined;
      const fieldName = field?.[0] || 'field';
      return createError(`${fieldName} already exists`, 409);
    
    case 'P2025':
      // Record not found
      return createError('Record not found', 404);
    
    case 'P2003':
      // Foreign key constraint violation
      return createError('Related record not found', 400);
    
    case 'P2014':
      // Invalid ID
      return createError('Invalid ID provided', 400);
    
    case 'P2021':
      // Table does not exist
      return createError('Database table not found', 500);
    
    case 'P2022':
      // Column does not exist
      return createError('Database column not found', 500);
    
    default:
      logger.error('Unhandled Prisma error:', error);
      return createError('Database operation failed', 500);
  }
};

/**
 * Handle validation errors
 */
const handleValidationError = (error: any): AppError => {
  if (error.errors && Array.isArray(error.errors)) {
    const messages = error.errors.map((err: any) => err.msg || err.message).join(', ');
    return createError(`Validation failed: ${messages}`, 400);
  }
  return createError('Validation failed', 400);
};

/**
 * Handle JWT errors
 */
const handleJWTError = (error: Error): AppError => {
  if (error.name === 'JsonWebTokenError') {
    return createError('Invalid token', 401);
  }
  if (error.name === 'TokenExpiredError') {
    return createError('Token expired', 401);
  }
  return createError('Authentication failed', 401);
};

/**
 * Send error response in development
 */
const sendErrorDev = (err: AppError, res: Response) => {
  res.status(err.statusCode || 500).json({
    status: 'error',
    error: err,
    message: err.message,
    stack: err.stack,
  });
};

/**
 * Send error response in production
 */
const sendErrorProd = (err: AppError, res: Response) => {
  // Operational, trusted error: send message to client
  if (err.isOperational) {
    res.status(err.statusCode || 500).json({
      status: 'error',
      message: err.message,
    });
  } else {
    // Programming or other unknown error: don't leak error details
    logger.error('Unknown error:', err);
    
    res.status(500).json({
      status: 'error',
      message: 'Something went wrong!',
    });
  }
};

/**
 * Global error handling middleware
 */
export const errorHandler = (
  err: any,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let error: AppError = { ...err };
  error.message = err.message;

  // Log error
  logger.error(`Error ${err.statusCode || 500}: ${err.message}`, {
    error: err,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: (req as any).user?.id,
  });

  // Handle specific error types
  if (err instanceof Prisma.PrismaClientKnownRequestError) {
    error = handlePrismaError(err);
  } else if (err instanceof Prisma.PrismaClientValidationError) {
    error = createError('Invalid data provided', 400);
  } else if (err.name === 'ValidationError' || err.errors) {
    error = handleValidationError(err);
  } else if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
    error = handleJWTError(err);
  } else if (err.name === 'CastError') {
    error = createError('Invalid ID format', 400);
  } else if (err.code === 11000) {
    // MongoDB duplicate key error (if using MongoDB in future)
    error = createError('Duplicate field value', 409);
  } else if (!err.statusCode) {
    // Unknown error
    error.statusCode = 500;
    error.isOperational = false;
  }

  // Send error response
  if (process.env.NODE_ENV === 'development') {
    sendErrorDev(error, res);
  } else {
    sendErrorProd(error, res);
  }
};

/**
 * Async error wrapper
 * Wraps async route handlers to catch errors
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 404 Not Found handler
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction) => {
  const error = createError(`Route ${req.originalUrl} not found`, 404);
  next(error);
};
