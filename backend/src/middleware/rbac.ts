/**
 * RBAC Middleware
 * Role-based access control middleware for API endpoints
 */

import { Request, Response, NextFunction } from 'express';
import { rbacService } from '@/services/rbacService';
import { createError, asyncHandler } from './errorHandler';
import { logger, logSecurity } from '@/utils/logger';

// Extend Request interface to include permissions
declare global {
  namespace Express {
    interface Request {
      userPermissions?: {
        roles: string[];
        permissions: string[];
      };
    }
  }
}

/**
 * Load user permissions middleware
 * Loads and caches user permissions for the request
 */
export const loadUserPermissions = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return next();
  }

  try {
    const userPermissions = await rbacService.getUserPermissions(req.user.id);
    
    req.userPermissions = {
      roles: userPermissions.roles.map(role => role.name),
      permissions: userPermissions.permissions,
    };

    // Also add permissions to the user object for convenience
    req.user.permissions = userPermissions.permissions;

    next();
  } catch (error) {
    logger.error('Failed to load user permissions:', error);
    next();
  }
});

/**
 * Require specific permission
 */
export const requirePermission = (permissionName: string) => {
  return asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      logSecurity('Unauthorized access attempt', 'medium', {
        endpoint: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      throw createError('Authentication required', 401);
    }

    const hasPermission = await rbacService.hasPermission(req.user.id, permissionName);
    
    if (!hasPermission) {
      logSecurity('Insufficient permissions', 'medium', {
        userId: req.user.id,
        email: req.user.email,
        requiredPermission: permissionName,
        endpoint: req.originalUrl,
        method: req.method,
        ip: req.ip,
      });
      throw createError('Insufficient permissions', 403);
    }

    next();
  });
};

/**
 * Require any of the specified permissions
 */
export const requireAnyPermission = (permissionNames: string[]) => {
  return asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      logSecurity('Unauthorized access attempt', 'medium', {
        endpoint: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      throw createError('Authentication required', 401);
    }

    const hasAnyPermission = await rbacService.hasAnyPermission(req.user.id, permissionNames);
    
    if (!hasAnyPermission) {
      logSecurity('Insufficient permissions', 'medium', {
        userId: req.user.id,
        email: req.user.email,
        requiredPermissions: permissionNames,
        endpoint: req.originalUrl,
        method: req.method,
        ip: req.ip,
      });
      throw createError('Insufficient permissions', 403);
    }

    next();
  });
};

/**
 * Require all specified permissions
 */
export const requireAllPermissions = (permissionNames: string[]) => {
  return asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      logSecurity('Unauthorized access attempt', 'medium', {
        endpoint: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      throw createError('Authentication required', 401);
    }

    const hasAllPermissions = await rbacService.hasAllPermissions(req.user.id, permissionNames);
    
    if (!hasAllPermissions) {
      logSecurity('Insufficient permissions', 'medium', {
        userId: req.user.id,
        email: req.user.email,
        requiredPermissions: permissionNames,
        endpoint: req.originalUrl,
        method: req.method,
        ip: req.ip,
      });
      throw createError('Insufficient permissions', 403);
    }

    next();
  });
};

/**
 * Require specific role
 */
export const requireRole = (roleName: string) => {
  return asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      logSecurity('Unauthorized access attempt', 'medium', {
        endpoint: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      throw createError('Authentication required', 401);
    }

    const hasRole = await rbacService.hasRole(req.user.id, roleName);
    
    if (!hasRole) {
      logSecurity('Insufficient role privileges', 'medium', {
        userId: req.user.id,
        email: req.user.email,
        requiredRole: roleName,
        endpoint: req.originalUrl,
        method: req.method,
        ip: req.ip,
      });
      throw createError('Insufficient permissions', 403);
    }

    next();
  });
};

/**
 * Require any of the specified roles
 */
export const requireAnyRole = (roleNames: string[]) => {
  return asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      logSecurity('Unauthorized access attempt', 'medium', {
        endpoint: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      throw createError('Authentication required', 401);
    }

    const hasAnyRole = await rbacService.hasAnyRole(req.user.id, roleNames);
    
    if (!hasAnyRole) {
      logSecurity('Insufficient role privileges', 'medium', {
        userId: req.user.id,
        email: req.user.email,
        requiredRoles: roleNames,
        endpoint: req.originalUrl,
        method: req.method,
        ip: req.ip,
      });
      throw createError('Insufficient permissions', 403);
    }

    next();
  });
};

/**
 * Resource ownership check
 * Allows access if user owns the resource or has admin privileges
 */
export const requireOwnershipOrRole = (
  resourceUserIdField: string = 'userId',
  allowedRoles: string[] = ['admin', 'super_admin']
) => {
  return asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw createError('Authentication required', 401);
    }

    // Get resource user ID from params or body
    const resourceUserId = req.params[resourceUserIdField] || req.body[resourceUserIdField];
    
    // Allow if user owns the resource
    if (req.user.id === resourceUserId) {
      return next();
    }

    // Check if user has any of the allowed roles
    const hasAllowedRole = await rbacService.hasAnyRole(req.user.id, allowedRoles);
    
    if (!hasAllowedRole) {
      logSecurity('Unauthorized resource access attempt', 'high', {
        userId: req.user.id,
        email: req.user.email,
        resourceUserId,
        allowedRoles,
        endpoint: req.originalUrl,
        method: req.method,
        ip: req.ip,
      });
      throw createError('Access denied', 403);
    }

    next();
  });
};

/**
 * Admin role requirement (admin or super_admin)
 */
export const requireAdmin = requireAnyRole(['admin', 'super_admin']);

/**
 * Super admin role requirement
 */
export const requireSuperAdmin = requireRole('super_admin');

/**
 * Member or higher role requirement (member, admin, or super_admin)
 */
export const requireMember = requireAnyRole(['member', 'admin', 'super_admin']);

/**
 * Project management permissions
 */
export const requireProjectRead = requirePermission('projects.read');
export const requireProjectCreate = requirePermission('projects.create');
export const requireProjectUpdate = requirePermission('projects.update');
export const requireProjectDelete = requirePermission('projects.delete');

/**
 * Task management permissions
 */
export const requireTaskRead = requirePermission('tasks.read');
export const requireTaskCreate = requirePermission('tasks.create');
export const requireTaskUpdate = requirePermission('tasks.update');
export const requireTaskDelete = requirePermission('tasks.delete');

/**
 * User management permissions
 */
export const requireUserRead = requirePermission('users.read');
export const requireUserCreate = requirePermission('users.create');
export const requireUserUpdate = requirePermission('users.update');
export const requireUserDelete = requirePermission('users.delete');

/**
 * Role management permissions
 */
export const requireRoleRead = requirePermission('roles.read');
export const requireRoleAssign = requirePermission('roles.assign');
export const requireRoleManage = requirePermission('roles.manage');

/**
 * System permissions
 */
export const requireSystemAdmin = requirePermission('system.admin');
export const requireSystemBackup = requirePermission('system.backup');
export const requireSystemLogs = requirePermission('system.logs');
