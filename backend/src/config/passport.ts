/**
 * Passport Configuration
 * Handles Google OAuth 2.0 strategy setup
 */

import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import { Strategy as JwtStrategy, ExtractJwt } from 'passport-jwt';
import { authService } from '@/services/authService';
import { logger } from '@/utils/logger';
import { createError } from '@/middleware/errorHandler';

// Google OAuth Strategy
passport.use(
  new GoogleStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      callbackURL: process.env.GOOGLE_REDIRECT_URI || '/api/v1/auth/google/callback',
    },
    async (accessToken, refreshToken, profile, done) => {
      try {
        logger.debug('Google OAuth profile received:', {
          id: profile.id,
          email: profile.emails?.[0]?.value,
          name: profile.displayName,
        });

        // Extract user info from Google profile
        const googleUser = {
          id: profile.id,
          email: profile.emails?.[0]?.value || '',
          name: profile.displayName || '',
          picture: profile.photos?.[0]?.value,
          verified_email: profile.emails?.[0]?.verified || false,
        };

        if (!googleUser.email) {
          return done(createError('Email not provided by Google', 400));
        }

        // Handle authentication through our service
        const result = await authService.handleGoogleAuth(googleUser);
        
        return done(null, result);
      } catch (error) {
        logger.error('Google OAuth strategy error:', error);
        return done(error);
      }
    }
  )
);

// JWT Strategy for API authentication
passport.use(
  new JwtStrategy(
    {
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: process.env.JWT_SECRET!,
      algorithms: ['HS256'],
    },
    async (payload, done) => {
      try {
        const user = await authService.getUserById(payload.id);
        
        if (!user.isActive) {
          return done(createError('Account deactivated', 401));
        }

        return done(null, {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
        });
      } catch (error) {
        logger.error('JWT strategy error:', error);
        return done(error);
      }
    }
  )
);

// Serialize user for session (not used in JWT setup, but required by Passport)
passport.serializeUser((user: any, done) => {
  done(null, user.id);
});

passport.deserializeUser(async (id: string, done) => {
  try {
    const user = await authService.getUserById(id);
    done(null, user);
  } catch (error) {
    done(error);
  }
});

export default passport;
