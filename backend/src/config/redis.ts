/**
 * Redis Configuration
 * Handles Redis connection for caching and session management
 */

import Redis from 'ioredis';
import { logger } from '@/utils/logger';

// Redis client instance
let redisClient: Redis | null = null;

/**
 * Create Redis client with configuration
 */
const createRedisClient = (): Redis => {
  const redisUrl = process.env.REDIS_URL || 'redis://:redis123@localhost:6379';
  
  const client = new Redis(redisUrl, {
    retryDelayOnFailover: 100,
    enableReadyCheck: true,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
    keepAlive: 30000,
    connectTimeout: 10000,
    commandTimeout: 5000,
  });

  // Event handlers
  client.on('connect', () => {
    logger.info('✅ Redis connection established');
  });

  client.on('ready', () => {
    logger.info('✅ Redis client ready');
  });

  client.on('error', (error) => {
    logger.error('❌ Redis connection error:', error);
  });

  client.on('close', () => {
    logger.warn('⚠️ Redis connection closed');
  });

  client.on('reconnecting', () => {
    logger.info('🔄 Redis reconnecting...');
  });

  return client;
};

/**
 * Connect to Redis
 */
export const connectRedis = async (): Promise<void> => {
  try {
    if (!redisClient) {
      redisClient = createRedisClient();
    }
    
    await redisClient.connect();
    logger.info('✅ Redis connected successfully');
    
    // Test the connection
    await redisClient.ping();
    logger.info('✅ Redis connection verified');
  } catch (error) {
    logger.error('❌ Redis connection failed:', error);
    throw error;
  }
};

/**
 * Disconnect from Redis
 */
export const disconnectRedis = async (): Promise<void> => {
  try {
    if (redisClient) {
      await redisClient.quit();
      redisClient = null;
      logger.info('✅ Redis disconnected');
    }
  } catch (error) {
    logger.error('❌ Redis disconnection failed:', error);
    throw error;
  }
};

/**
 * Get Redis client instance
 */
export const getRedisClient = (): Redis => {
  if (!redisClient) {
    throw new Error('Redis client not initialized. Call connectRedis() first.');
  }
  return redisClient;
};

/**
 * Health check for Redis connection
 */
export const checkRedisHealth = async (): Promise<boolean> => {
  try {
    if (!redisClient) return false;
    const result = await redisClient.ping();
    return result === 'PONG';
  } catch (error) {
    logger.error('Redis health check failed:', error);
    return false;
  }
};

/**
 * Cache helper functions
 */
export const cache = {
  /**
   * Set a value in cache with optional TTL
   */
  set: async (key: string, value: any, ttlSeconds?: number): Promise<void> => {
    const client = getRedisClient();
    const serializedValue = JSON.stringify(value);
    
    if (ttlSeconds) {
      await client.setex(key, ttlSeconds, serializedValue);
    } else {
      await client.set(key, serializedValue);
    }
  },

  /**
   * Get a value from cache
   */
  get: async <T = any>(key: string): Promise<T | null> => {
    const client = getRedisClient();
    const value = await client.get(key);
    
    if (!value) return null;
    
    try {
      return JSON.parse(value) as T;
    } catch (error) {
      logger.error(`Failed to parse cached value for key ${key}:`, error);
      return null;
    }
  },

  /**
   * Delete a value from cache
   */
  del: async (key: string): Promise<void> => {
    const client = getRedisClient();
    await client.del(key);
  },

  /**
   * Check if a key exists in cache
   */
  exists: async (key: string): Promise<boolean> => {
    const client = getRedisClient();
    const result = await client.exists(key);
    return result === 1;
  },

  /**
   * Set expiration for a key
   */
  expire: async (key: string, ttlSeconds: number): Promise<void> => {
    const client = getRedisClient();
    await client.expire(key, ttlSeconds);
  },

  /**
   * Get all keys matching a pattern
   */
  keys: async (pattern: string): Promise<string[]> => {
    const client = getRedisClient();
    return client.keys(pattern);
  },

  /**
   * Clear all cache
   */
  flush: async (): Promise<void> => {
    const client = getRedisClient();
    await client.flushall();
  },
};

export default redisClient;
