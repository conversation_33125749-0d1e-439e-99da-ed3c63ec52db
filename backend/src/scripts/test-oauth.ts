/**
 * OAuth Test Script
 * Tests Google OAuth configuration and endpoints
 */

import { connectDatabase } from '../config/database';
import { connectRedis } from '../config/redis';
import { logger } from '../utils/logger';

async function testOAuthSetup() {
  try {
    console.log('🔐 Testing Google OAuth Setup...\n');

    // Initialize connections
    await connectDatabase();
    await connectRedis();
    console.log('✅ Database and Redis connected\n');

    // Check environment variables
    console.log('1. Checking environment variables...');
    const requiredEnvVars = [
      'GOOGLE_CLIENT_ID',
      'GOOGLE_CLIENT_SECRET',
      'GOOGLE_REDIRECT_URI',
      'JWT_SECRET',
      'JWT_REFRESH_SECRET',
    ];

    let allEnvVarsSet = true;
    for (const envVar of requiredEnvVars) {
      const value = process.env[envVar];
      if (!value || value.includes('your-') || value.includes('change-this')) {
        console.log(`❌ ${envVar}: Not properly configured`);
        allEnvVarsSet = false;
      } else {
        console.log(`✅ ${envVar}: Configured (${value.substring(0, 10)}...)`);
      }
    }

    if (!allEnvVarsSet) {
      console.log('\n⚠️ Please update your .env file with proper Google OAuth credentials');
      console.log('📖 Follow the setup guide to get your credentials from Google Cloud Console');
      return;
    }

    // Test OAuth URLs
    console.log('\n2. Testing OAuth URLs...');
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:8080';
    const apiUrl = 'http://localhost:3000/api/v1';
    
    console.log(`✅ Frontend URL: ${baseUrl}`);
    console.log(`✅ API Base URL: ${apiUrl}`);
    console.log(`✅ OAuth Initiate: ${apiUrl}/auth/google`);
    console.log(`✅ OAuth Callback: ${process.env.GOOGLE_REDIRECT_URI}`);

    // Test Google OAuth URL generation
    console.log('\n3. Testing Google OAuth URL generation...');
    const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
      `client_id=${process.env.GOOGLE_CLIENT_ID}&` +
      `redirect_uri=${encodeURIComponent(process.env.GOOGLE_REDIRECT_URI!)}&` +
      `response_type=code&` +
      `scope=${encodeURIComponent('openid profile email')}&` +
      `access_type=offline&` +
      `prompt=consent`;

    console.log('✅ Google OAuth URL generated successfully');
    console.log(`   Length: ${googleAuthUrl.length} characters`);

    // Test server endpoints availability
    console.log('\n4. Testing server endpoints...');
    try {
      const response = await fetch('http://localhost:3000/health');
      if (response.ok) {
        console.log('✅ Backend server is running');
        const healthData = await response.json();
        console.log(`   Status: ${healthData.status}`);
        console.log(`   Timestamp: ${healthData.timestamp}`);
      } else {
        console.log('❌ Backend server health check failed');
      }
    } catch (error) {
      console.log('❌ Backend server is not running');
      console.log('   Please start the backend server with: npm run start:dev');
    }

    // Instructions for manual testing
    console.log('\n📋 Manual Testing Instructions:');
    console.log('1. Make sure backend server is running: npm run start:dev');
    console.log('2. Make sure frontend server is running: npm run dev');
    console.log('3. Open browser to: http://localhost:8080');
    console.log('4. Click "Sign in with Google" button');
    console.log('5. Complete Google OAuth flow');
    console.log('6. Check if you\'re redirected back with authentication');

    console.log('\n🔗 Test URLs:');
    console.log(`Frontend: http://localhost:8080`);
    console.log(`Backend Health: http://localhost:3000/health`);
    console.log(`OAuth Initiate: ${apiUrl}/auth/google`);

    console.log('\n🎯 Expected Flow:');
    console.log('1. User clicks "Sign in with Google"');
    console.log('2. Frontend redirects to: /api/v1/auth/google');
    console.log('3. Backend redirects to Google OAuth');
    console.log('4. User authorizes on Google');
    console.log('5. Google redirects to: /api/v1/auth/google/callback');
    console.log('6. Backend processes OAuth code');
    console.log('7. Backend creates/updates user');
    console.log('8. Backend assigns guest role');
    console.log('9. Backend generates JWT tokens');
    console.log('10. Backend redirects to frontend with tokens');

    console.log('\n✅ OAuth setup test completed!');

  } catch (error) {
    console.error('❌ OAuth setup test failed:', error);
    throw error;
  }
}

// Run the OAuth test
if (require.main === module) {
  testOAuthSetup()
    .then(() => {
      console.log('\n🎉 OAuth setup test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ OAuth setup test failed:', error);
      process.exit(1);
    });
}

export { testOAuthSetup };
