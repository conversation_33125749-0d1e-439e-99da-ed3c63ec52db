/**
 * Seed Test Data Script
 * Creates sample projects and tasks for testing the new API
 */

import { prisma, connectDatabase } from '../config/database';
import { connectRedis } from '../config/redis';
import { rbacService } from '../services/rbacService';
import { logger } from '../utils/logger';

async function seedTestData() {
  try {
    console.log('🌱 Seeding test data...\n');

    // Initialize connections
    await connectDatabase();
    await connectRedis();
    console.log('✅ Database and Redis connected\n');

    // Create a test user
    console.log('1. Creating test user...');
    const testUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Test User',
        role: 'member',
        isActive: true,
      },
    });
    console.log(`✅ Test user: ${testUser.email} (ID: ${testUser.id})`);

    // Assign member role
    await rbacService.initializeGuestRole(testUser.id);
    await rbacService.assignRole(testUser.id, 'member');
    console.log('✅ Assigned member role to test user');

    // Create sample projects
    console.log('\n2. Creating sample projects...');
    
    const project1 = await prisma.project.create({
      data: {
        name: 'E-commerce Platform Redesign',
        type: 'external',
        customerName: 'TechCorp Inc.',
        projectLead: 'John <PERSON>',
        customerLead: 'Jane Doe',
        customerContact: '<EMAIL>',
        description: 'Complete redesign of the e-commerce platform with modern UI/UX and improved performance.',
        status: 'active',
        priorityLevel: 'P1',
        effortEstimate: 'XL',
        createdBy: testUser.id,
      },
    });

    const project2 = await prisma.project.create({
      data: {
        name: 'Internal Dashboard Development',
        type: 'internal',
        projectLead: 'Alice Johnson',
        description: 'Development of internal analytics dashboard for business intelligence.',
        status: 'active',
        priorityLevel: 'P2',
        effortEstimate: 'L',
        createdBy: testUser.id,
      },
    });

    const project3 = await prisma.project.create({
      data: {
        name: 'Mobile App MVP',
        type: 'external',
        customerName: 'StartupXYZ',
        projectLead: 'Bob Wilson',
        customerLead: 'Mike Chen',
        customerContact: '<EMAIL>',
        description: 'Minimum viable product for mobile application with core features.',
        status: 'active',
        priorityLevel: 'P0',
        effortEstimate: 'M',
        createdBy: testUser.id,
      },
    });

    console.log(`✅ Created 3 sample projects`);

    // Create sample tasks
    console.log('\n3. Creating sample tasks...');
    
    const tasks = [
      // Project 1 tasks
      {
        projectId: project1.id,
        name: 'UI/UX Design Research',
        description: 'Research current design trends and user preferences',
        status: 'done',
        priorityLevel: 'P1',
        effortEstimate: 'M',
      },
      {
        projectId: project1.id,
        name: 'Frontend Development Setup',
        description: 'Set up React development environment and tooling',
        status: 'in_progress',
        priorityLevel: 'P1',
        effortEstimate: 'S',
      },
      {
        projectId: project1.id,
        name: 'Backend API Development',
        description: 'Develop REST APIs for product catalog and user management',
        status: 'to_do',
        priorityLevel: 'P1',
        effortEstimate: 'L',
      },
      
      // Project 2 tasks
      {
        projectId: project2.id,
        name: 'Database Schema Design',
        description: 'Design database schema for analytics data',
        status: 'done',
        priorityLevel: 'P2',
        effortEstimate: 'M',
      },
      {
        projectId: project2.id,
        name: 'Dashboard Components',
        description: 'Create reusable dashboard components',
        status: 'in-progress',
        priorityLevel: 'P2',
        effortEstimate: 'L',
      },
      
      // Project 3 tasks
      {
        projectId: project3.id,
        name: 'App Architecture Planning',
        description: 'Plan mobile app architecture and technology stack',
        status: 'done',
        priorityLevel: 'P0',
        effortEstimate: 'S',
      },
      {
        projectId: project3.id,
        name: 'Core Feature Development',
        description: 'Develop core MVP features',
        status: 'in-progress',
        priorityLevel: 'P0',
        effortEstimate: 'XL',
      },
      {
        projectId: project3.id,
        name: 'Testing & QA',
        description: 'Comprehensive testing of MVP features',
        status: 'to-do',
        priorityLevel: 'P0',
        effortEstimate: 'M',
      },
    ];

    for (const taskData of tasks) {
      await prisma.task.create({
        data: {
          ...taskData,
          createdBy: testUser.id,
        } as any,
      });
    }

    console.log(`✅ Created ${tasks.length} sample tasks`);

    // Display summary
    console.log('\n📊 Test Data Summary:');
    const projectCount = await prisma.project.count();
    const taskCount = await prisma.task.count();
    const userCount = await prisma.user.count();
    
    console.log(`  👥 Users: ${userCount}`);
    console.log(`  📁 Projects: ${projectCount}`);
    console.log(`  ✅ Tasks: ${taskCount}`);

    console.log('\n🎉 Test data seeded successfully!');
    console.log('\n🔗 You can now test the API endpoints:');
    console.log('  GET /api/v1/projects - List all projects');
    console.log('  GET /api/v1/tasks - List all tasks');
    console.log('  GET /api/v1/rbac/roles - List all roles');

  } catch (error) {
    console.error('❌ Failed to seed test data:', error);
    throw error;
  }
}

// Run the seeding
if (require.main === module) {
  seedTestData()
    .then(() => {
      console.log('\n✅ Seeding completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Seeding failed:', error);
      process.exit(1);
    });
}

export { seedTestData };
