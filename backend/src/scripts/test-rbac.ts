/**
 * RBAC System Test Script
 * Tests the role-based access control system
 */

import { prisma, connectDatabase } from '../config/database';
import { connectRedis } from '../config/redis';
import { rbacService } from '../services/rbacService';
import { authService } from '../services/authService';
import { logger } from '../utils/logger';

async function testRBACSystem() {
  try {
    console.log('🧪 Testing RBAC System...\n');

    // Initialize connections
    console.log('0. Initializing connections...');
    await connectDatabase();
    await connectRedis();
    console.log('✅ Database and Redis connected\n');

    // 1. Test getting all roles
    console.log('1. Testing getAllRoles()...');

    // Clear cache first
    await rbacService.clearAllCaches();

    // Check database directly
    const rolesFromDB = await prisma.role.findMany();
    console.log(`📊 Direct DB query found ${rolesFromDB.length} roles:`, rolesFromDB.map(r => r.name).join(', '));

    const roles = await rbacService.getAllRoles();
    console.log(`✅ Service found ${roles.length} roles:`, roles.map(r => r.name).join(', '));

    // 2. Test getting all permissions
    console.log('\n2. Testing getAllPermissions()...');
    const permissionsFromDB = await prisma.permission.findMany();
    console.log(`📊 Direct DB query found ${permissionsFromDB.length} permissions`);

    const permissions = await rbacService.getAllPermissions();
    console.log(`✅ Service found ${permissions.length} permissions:`, permissions.slice(0, 5).map(p => p.name).join(', '), '...');

    // 3. Create a test user (or get existing one)
    console.log('\n3. Creating test user...');

    // Delete existing test user if it exists
    await prisma.user.deleteMany({
      where: { email: '<EMAIL>' },
    });

    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test User',
        role: 'guest',
        isActive: true,
      },
    });
    console.log(`✅ Created test user: ${testUser.email} (ID: ${testUser.id})`);

    // 4. Initialize guest role
    console.log('\n4. Initializing guest role...');
    await rbacService.initializeGuestRole(testUser.id);
    console.log('✅ Guest role initialized');

    // 5. Test user permissions
    console.log('\n5. Testing user permissions...');
    const userPermissions = await rbacService.getUserPermissions(testUser.id);
    console.log(`✅ User has ${userPermissions.roles.length} roles and ${userPermissions.permissions.length} permissions`);
    console.log('Roles:', userPermissions.roles.map(r => r.name).join(', '));
    console.log('Permissions:', userPermissions.permissions.slice(0, 5).join(', '), '...');

    // 6. Test permission checks
    console.log('\n6. Testing permission checks...');
    const hasProjectsRead = await rbacService.hasPermission(testUser.id, 'projects.read');
    const hasProjectsCreate = await rbacService.hasPermission(testUser.id, 'projects.create');
    console.log(`✅ Has 'projects.read': ${hasProjectsRead}`);
    console.log(`✅ Has 'projects.create': ${hasProjectsCreate}`);

    // 7. Test role checks
    console.log('\n7. Testing role checks...');
    const hasGuestRole = await rbacService.hasRole(testUser.id, 'guest');
    const hasAdminRole = await rbacService.hasRole(testUser.id, 'admin');
    console.log(`✅ Has 'guest' role: ${hasGuestRole}`);
    console.log(`✅ Has 'admin' role: ${hasAdminRole}`);

    // 8. Test role assignment
    console.log('\n8. Testing role assignment...');
    await rbacService.assignRole(testUser.id, 'member');
    const updatedPermissions = await rbacService.getUserPermissions(testUser.id);
    console.log(`✅ After assigning 'member' role: ${updatedPermissions.roles.length} roles, ${updatedPermissions.permissions.length} permissions`);
    console.log('New roles:', updatedPermissions.roles.map(r => r.name).join(', '));

    // 9. Test permission checks after role assignment
    console.log('\n9. Testing permissions after role assignment...');
    const hasProjectsCreateAfter = await rbacService.hasPermission(testUser.id, 'projects.create');
    console.log(`✅ Has 'projects.create' after member role: ${hasProjectsCreateAfter}`);

    // 10. Test role removal
    console.log('\n10. Testing role removal...');
    await rbacService.removeRole(testUser.id, 'member');
    const finalPermissions = await rbacService.getUserPermissions(testUser.id);
    console.log(`✅ After removing 'member' role: ${finalPermissions.roles.length} roles, ${finalPermissions.permissions.length} permissions`);
    console.log('Final roles:', finalPermissions.roles.map(r => r.name).join(', '));

    // Cleanup
    console.log('\n11. Cleaning up...');
    await prisma.user.delete({ where: { id: testUser.id } });
    console.log('✅ Test user deleted');

    console.log('\n🎉 RBAC System test completed successfully!');

  } catch (error) {
    console.error('❌ RBAC System test failed:', error);
    throw error;
  }
}

// Run the test
if (require.main === module) {
  testRBACSystem()
    .then(() => {
      console.log('\n✅ All tests passed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Tests failed:', error);
      process.exit(1);
    });
}

export { testRBACSystem };
