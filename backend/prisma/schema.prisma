// Core Project Pulse - Prisma Schema
// This schema defines the database structure for our enterprise project management platform

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  guest
  member
  admin
  super_admin
}

enum ProjectStatus {
  active
  completed
  on_hold
  cancelled
}

enum TaskStatus {
  to_do
  in_progress
  done
  cancelled
}

enum PriorityLevel {
  P0
  P1
  P2
  P3
  P4
}

enum EffortEstimate {
  XS
  S
  M
  L
  XL
}

// Core Models
model User {
  id            String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email         String    @unique
  googleId      String?   @unique @map("google_id")
  name          String
  avatarUrl     String?   @map("avatar_url")
  role          UserRole  @default(guest)
  isActive      Boolean   @default(true) @map("is_active")
  lastLoginAt   DateTime? @map("last_login_at")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  // Relations
  createdProjects Project[] @relation("ProjectCreator")
  assignedTasks   Task[]    @relation("TaskAssignee")
  createdTasks    Task[]    @relation("TaskCreator")
  userRoles       UserRole_Relation[]
  assignedRoles   UserRole_Relation[] @relation("RoleAssigner")

  @@map("users")
}

model Role {
  id           String  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name         String  @unique
  description  String?
  isSystemRole Boolean @default(false) @map("is_system_role")
  createdAt    DateTime @default(now()) @map("created_at")

  // Relations
  permissions RolePermission[]
  userRoles   UserRole_Relation[]

  @@map("roles")
}

model Permission {
  id          String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name        String @unique
  resource    String
  action      String
  description String?
  createdAt   DateTime @default(now()) @map("created_at")

  // Relations
  roles RolePermission[]

  @@map("permissions")
}

model RolePermission {
  roleId       String @map("role_id") @db.Uuid
  permissionId String @map("permission_id") @db.Uuid

  // Relations
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@id([roleId, permissionId])
  @@map("role_permissions")
}

model UserRole_Relation {
  userId     String   @map("user_id") @db.Uuid
  roleId     String   @map("role_id") @db.Uuid
  assignedBy String?  @map("assigned_by") @db.Uuid
  assignedAt DateTime @default(now()) @map("assigned_at")

  // Relations
  user     User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  role     Role  @relation(fields: [roleId], references: [id], onDelete: Cascade)
  assigner User? @relation("RoleAssigner", fields: [assignedBy], references: [id])

  @@id([userId, roleId])
  @@map("user_roles")
}

model Project {
  id              String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name            String
  type            String // 'internal' | 'external'
  customerName    String?        @map("customer_name")
  projectLead     String         @map("project_lead")
  customerLead    String?        @map("customer_lead")
  customerContact String?        @map("customer_contact")
  description     String?
  status          ProjectStatus  @default(active)
  priorityLevel   PriorityLevel  @default(P3) @map("priority_level")
  effortEstimate  EffortEstimate @default(M) @map("effort_estimate")
  createdBy       String?        @map("created_by") @db.Uuid
  createdAt       DateTime       @default(now()) @map("created_at")
  updatedAt       DateTime       @updatedAt @map("updated_at")

  // Relations
  creator User? @relation("ProjectCreator", fields: [createdBy], references: [id])
  tasks   Task[]

  @@map("projects")
}

model Task {
  id              String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  projectId       String         @map("project_id") @db.Uuid
  name            String
  description     String?
  assigneeId      String?        @map("assignee_id") @db.Uuid
  dueDate         DateTime?      @map("due_date") @db.Date
  status          TaskStatus     @default(to_do)
  priorityLevel   PriorityLevel  @default(P3) @map("priority_level")
  effortEstimate  EffortEstimate @default(M) @map("effort_estimate")
  createdBy       String?        @map("created_by") @db.Uuid
  createdAt       DateTime       @default(now()) @map("created_at")
  updatedAt       DateTime       @updatedAt @map("updated_at")

  // Relations
  project  Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  assignee User?   @relation("TaskAssignee", fields: [assigneeId], references: [id])
  creator  User?   @relation("TaskCreator", fields: [createdBy], references: [id])

  @@map("tasks")
}
